import { useEffect, useState } from 'react'
import Lenis from 'lenis'
import { motion } from 'framer-motion'
import Header from './components/Header'
import Hero from './components/Hero'
import PopUpEvents from './components/PopUpEvents'
import ProductShowcase from './components/ProductShowcase'
import BrandStory from './components/BrandStory'
import Footer from './components/Footer'
import CustomCursor from './components/CustomCursor'
import ScrollProgress from './components/ScrollProgress'
import LoadingScreen from './components/LoadingScreen'

function App() {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Initialize smooth scrolling with Lenis
    const lenis = new Lenis({
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
      direction: 'vertical',
      gestureDirection: 'vertical',
      smooth: true,
      mouseMultiplier: 1,
      smoothTouch: false,
      touchMultiplier: 2,
      infinite: false,
    })

    function raf(time: number) {
      lenis.raf(time)
      requestAnimationFrame(raf)
    }

    requestAnimationFrame(raf)

    return () => {
      lenis.destroy()
    }
  }, [])

  return (
    <>
      {isLoading && <LoadingScreen onComplete={() => setIsLoading(false)} />}
      <div className="min-h-screen bg-cream-50 overflow-x-hidden relative">
        {/* Luxury Background with Depth */}
        <div className="fixed inset-0 z-0">
          {/* Main luxury fashion background */}
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-5"
            style={{
              backgroundImage: `url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80')`
            }}
          />
          {/* Overlay for depth and luxury feel */}
          <div className="absolute inset-0 bg-gradient-to-br from-cream-50/95 via-gold-50/90 to-rose-50/95" />
          {/* Subtle texture overlay */}
          <div
            className="absolute inset-0 opacity-10 mix-blend-multiply"
            style={{
              backgroundImage: `url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80')`
            }}
          />
        </div>

        {/* Content with proper z-index */}
        <div className="relative z-10">
          <CustomCursor />
          <ScrollProgress />
          <Header />
          <main>
            <Hero />
            <PopUpEvents />
            <ProductShowcase />
            <BrandStory />
          </main>
          <Footer />
        </div>
      </div>
    </>
  )
}

export default App
