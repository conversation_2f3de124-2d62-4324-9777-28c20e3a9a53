[build]
  # Build command
  command = "npm run build"
  
  # Directory to publish (this is where your built files will be)
  publish = "dist"
  
  # Node.js version
  environment = { NODE_VERSION = "18" }

[build.processing]
  # Skip processing of images, JS, and CSS (Vite already optimizes these)
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

# Headers for better performance and security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets for 1 year
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache images for 1 month
[[headers]]
  for = "*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=2592000"

[[headers]]
  for = "*.png"
  [headers.values]
    Cache-Control = "public, max-age=2592000"

[[headers]]
  for = "*.webp"
  [headers.values]
    Cache-Control = "public, max-age=2592000"

# Cache fonts for 1 year
[[headers]]
  for = "*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Redirects are handled by _redirects file
