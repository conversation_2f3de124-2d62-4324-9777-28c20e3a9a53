# Netlify redirects for SPA routing
# This ensures all routes are handled by the React app

# Handle all routes by serving index.html
/*    /index.html   200

# Optional: Redirect www to non-www (uncomment if needed)
# https://www.fashionistasattic.com/* https://fashionistasattic.com/:splat 301!

# Optional: Force HTTPS (uncomment if needed)
# http://fashionistasattic.com/* https://fashionistasattic.com/:splat 301!
