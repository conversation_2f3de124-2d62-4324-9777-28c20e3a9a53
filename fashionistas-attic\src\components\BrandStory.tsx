import { motion } from 'framer-motion'
import { Heart, Award, Users, <PERSON>rk<PERSON>, Star, Shield } from 'lucide-react'

const BrandStory = () => {
  const values = [
    {
      icon: Heart,
      title: 'Quality First',
      description: 'Every piece is carefully selected for superior quality and craftsmanship that lasts.',
    },
    {
      icon: Award,
      title: 'Affordable Luxury',
      description: 'Premium fashion that doesn\'t break the bank. Luxury should be accessible to everyone.',
    },
    {
      icon: Users,
      title: 'Community Focused',
      description: 'Building connections through pop-up events and creating a fashion-loving community.',
    },
    {
      icon: Shield,
      title: 'Trusted Choice',
      description: 'Over 500 satisfied customers trust us for their fashion needs. Join our growing family.',
    },
  ]

  const stats = [
    { number: '500+', label: 'Happy Customers' },
    { number: '50+', label: 'Pop-Up Events' },
    { number: '1000+', label: 'Fashion Pieces' },
    { number: '4.9', label: 'Average Rating' },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: 'easeOut',
      },
    },
  }

  return (
    <section id="story" className="py-20 lg:py-32 relative overflow-hidden bg-cream-50">
      {/* Elegant Brand Story Background with Depth */}
      <div className="absolute inset-0">
        {/* Fashion designer workspace background */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-8"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80')`
          }}
        />
        {/* Luxury fashion atelier for depth */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-5 mix-blend-soft-light"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1445205170230-053b83016050?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80')`
          }}
        />
        {/* Light elegant gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-cream-50/95 via-pearl-50/90 to-cream-100/95" />
        {/* Subtle luxury texture */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gold-100/20 to-transparent" />
      </div>

      {/* Minimal decorative elements */}

      <div className="container-luxury relative z-10">
        {/* Main Story Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Story Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="flex items-center gap-2 mb-4">
              <Heart className="text-gold-600" size={24} />
              <span className="text-gold-700 font-medium uppercase tracking-wider text-sm">
                Our Story
              </span>
            </div>

            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-serif font-bold text-charcoal-900">
              Where{' '}
              <span className="text-gradient">Passion</span>
              {' '}Meets{' '}
              <span className="text-gradient">Purpose</span>
            </h2>

            <div className="space-y-4 text-charcoal-700 leading-relaxed">
              <p>
                Hi, I'm <strong className="text-charcoal-900">Lashonda</strong>, the founder of Fashionistas Attic Boutique.
                What started as a passion project has grown into a thriving community of fashion lovers who believe
                that style shouldn't come with an impossible price tag.
              </p>

              <p>
                After years of watching fast fashion dominate the market with poor quality and questionable practices,
                I decided to create something different. A boutique that offers <strong className="text-gold-700">premium quality</strong> pieces
                at prices that make sense for real people living real lives.
              </p>

              <p>
                Every piece in our collection is carefully curated for quality, style, and value.
                We're not just selling clothes – we're building a community where fashion is accessible,
                sustainable, and most importantly, <strong className="text-gold-600">authentic</strong>.
              </p>
            </div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              className="inline-block"
            >
              <button className="luxury-button">
                Learn More About Our Mission
              </button>
            </motion.div>
          </motion.div>

          {/* Story Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative overflow-hidden rounded-2xl shadow-luxury">
              <img
                src="https://images.unsplash.com/photo-1556905055-8f358a7a47b2?w=600&h=700&fit=crop&crop=center"
                alt="Lashonda - Founder of Fashionistas Attic"
                className="w-full h-96 lg:h-[500px] object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
              
              {/* Floating Quote */}
              <div className="absolute bottom-6 left-6 right-6">
                <div className="glass-effect rounded-xl p-4">
                  <p className="text-white text-sm italic">
                    "Fashion is about expressing who you are, not emptying your wallet."
                  </p>
                  <p className="text-gold-300 text-xs mt-2 font-medium">- Lashonda, Founder</p>
                </div>
              </div>
            </div>

            {/* Floating Stats */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
              className="absolute -top-6 -right-6 glass-effect rounded-xl p-4"
            >
              <div className="text-center">
                <div className="flex text-gold-400 justify-center mb-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} size={16} fill="currentColor" />
                  ))}
                </div>
                <div className="text-2xl font-bold text-charcoal-900">4.9</div>
                <div className="text-xs text-charcoal-600">Customer Rating</div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Values Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="mb-20"
        >
          <motion.div variants={itemVariants} className="text-center mb-12">
            <h3 className="text-2xl sm:text-3xl font-serif font-bold text-charcoal-900 mb-4">
              Our Core Values
            </h3>
            <p className="text-charcoal-600 max-w-2xl mx-auto">
              These principles guide everything we do, from selecting our collections to serving our community.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                variants={itemVariants}
                className="luxury-card text-center group"
              >
                <div className="w-16 h-16 bg-gold-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <value.icon className="text-white" size={24} />
                </div>
                <h4 className="text-lg font-serif font-bold text-charcoal-900 mb-2">
                  {value.title}
                </h4>
                <p className="text-charcoal-600 text-sm leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="luxury-card"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl sm:text-3xl font-serif font-bold text-charcoal-900 mb-4">
              Our Impact in Numbers
            </h3>
            <p className="text-charcoal-600">
              Building a community one satisfied customer at a time.
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-3xl lg:text-4xl font-bold text-gradient mb-2">
                  {stat.number}
                </div>
                <div className="text-charcoal-600 text-sm font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default BrandStory
