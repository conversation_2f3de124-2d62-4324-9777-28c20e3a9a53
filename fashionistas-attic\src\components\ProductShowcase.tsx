import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Heart, ShoppingBag, Star, Filter, Sparkles } from 'lucide-react'

const ProductShowcase = () => {
  const [activeCategory, setActiveCategory] = useState('all')

  const categories = [
    { id: 'all', name: 'All Collections', count: 24 },
    { id: 'women', name: 'Women\'s Fashion', count: 16 },
    { id: 'children', name: 'Children\'s Wear', count: 8 },
    { id: 'accessories', name: 'Accessories', count: 12 },
  ]

  const products = [
    {
      id: 1,
      name: 'Elegant Evening Dress',
      category: 'women',
      price: 89.99,
      originalPrice: 149.99,
      rating: 4.8,
      reviews: 24,
      image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=500&fit=crop&crop=center',
      badge: 'Best Seller',
      colors: ['#000000', '#8B4513', '#800080'],
    },
    {
      id: 2,
      name: '<PERSON><PERSON> Sweater',
      category: 'women',
      price: 45.99,
      originalPrice: 79.99,
      rating: 4.9,
      reviews: 18,
      image: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400&h=500&fit=crop&crop=center',
      badge: 'New Arrival',
      colors: ['#F5F5DC', '#D2691E', '#696969'],
    },
    {
      id: 3,
      name: 'Kids Rainbow Dress',
      category: 'children',
      price: 32.99,
      originalPrice: 54.99,
      rating: 4.7,
      reviews: 15,
      image: 'https://images.unsplash.com/photo-1518831959646-742c3a14ebf7?w=400&h=500&fit=crop&crop=center',
      badge: 'Sale',
      colors: ['#FF69B4', '#87CEEB', '#98FB98'],
    },
    {
      id: 4,
      name: 'Designer Handbag',
      category: 'accessories',
      price: 67.99,
      originalPrice: 119.99,
      rating: 4.6,
      reviews: 31,
      image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=500&fit=crop&crop=center',
      badge: 'Limited Edition',
      colors: ['#8B4513', '#000000', '#B8860B'],
    },
    {
      id: 5,
      name: 'Casual Summer Top',
      category: 'women',
      price: 28.99,
      originalPrice: 49.99,
      rating: 4.5,
      reviews: 22,
      image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=500&fit=crop&crop=center',
      badge: 'Trending',
      colors: ['#FFFFFF', '#FFB6C1', '#87CEEB'],
    },
    {
      id: 6,
      name: 'Boys Adventure Set',
      category: 'children',
      price: 39.99,
      originalPrice: 64.99,
      rating: 4.8,
      reviews: 12,
      image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=400&h=500&fit=crop&crop=center',
      badge: 'Popular',
      colors: ['#4169E1', '#228B22', '#FF6347'],
    },
  ]

  const filteredProducts = activeCategory === 'all' 
    ? products 
    : products.filter(product => product.category === activeCategory)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const productVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  }

  const getBadgeColor = (badge: string) => {
    switch (badge) {
      case 'Best Seller': return 'bg-gold-500'
      case 'New Arrival': return 'bg-green-500'
      case 'Sale': return 'bg-red-500'
      case 'Limited Edition': return 'bg-purple-500'
      case 'Trending': return 'bg-blue-500'
      case 'Popular': return 'bg-rose-500'
      default: return 'bg-gray-500'
    }
  }

  return (
    <section id="collections" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Luxury Product Background with Depth */}
      <div className="absolute inset-0">
        {/* Fashion collection background */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-8"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80')`
          }}
        />
        {/* Luxury boutique interior for depth */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-6 mix-blend-soft-light"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80')`
          }}
        />
        {/* Clean white overlay with subtle gradient */}
        <div className="absolute inset-0 bg-gradient-to-b from-white/95 via-white/90 to-cream-50/95" />
        {/* Subtle texture for luxury feel */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cream-50/20 to-transparent" />
      </div>

      {/* Background Elements */}
      <motion.div
        animate={{ x: [-100, 100, -100] }}
        transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
        className="absolute top-40 left-0 w-64 h-64 bg-gradient-radial from-gold-100 to-transparent rounded-full opacity-30"
      ></motion.div>

      <div className="container-luxury relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="text-rose-500" size={24} />
            <span className="text-rose-600 font-medium uppercase tracking-wider text-sm">
              Curated Collections
            </span>
            <Sparkles className="text-rose-500" size={24} />
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-serif font-bold text-charcoal-900 mb-6">
            Premium{' '}
            <span className="text-gradient">Fashion</span>
            {' '}Collections
          </h2>
          <p className="text-lg text-charcoal-600 max-w-2xl mx-auto">
            Discover our carefully curated selection of premium fashion pieces. 
            Quality that rivals luxury brands, prices that make sense.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <motion.button
              key={category.id}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActiveCategory(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-gradient-luxury from-gold-500 to-rose-500 text-white shadow-luxury'
                  : 'bg-white border-2 border-gold-200 text-charcoal-700 hover:border-gold-400'
              }`}
            >
              {category.name}
              <span className="ml-2 text-xs opacity-75">({category.count})</span>
            </motion.button>
          ))}
        </motion.div>

        {/* Products Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeCategory}
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {filteredProducts.map((product) => (
              <motion.div
                key={product.id}
                variants={productVariants}
                layout
                className="luxury-card group cursor-pointer"
              >
                {/* Product Image */}
                <div className="relative overflow-hidden rounded-xl mb-4">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300"></div>
                  
                  {/* Badge */}
                  <div className="absolute top-3 left-3">
                    <span className={`px-2 py-1 ${getBadgeColor(product.badge)} text-white text-xs font-medium rounded-full`}>
                      {product.badge}
                    </span>
                  </div>

                  {/* Action Buttons */}
                  <div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white transition-colors duration-300"
                    >
                      <Heart size={16} className="text-charcoal-700" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white transition-colors duration-300"
                    >
                      <ShoppingBag size={16} className="text-charcoal-700" />
                    </motion.button>
                  </div>

                  {/* Quick Add Button */}
                  <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full py-2 bg-white/90 backdrop-blur-sm text-charcoal-900 font-medium rounded-full 
                               hover:bg-white transition-all duration-300"
                    >
                      Quick Add to Cart
                    </motion.button>
                  </div>
                </div>

                {/* Product Info */}
                <div className="space-y-3">
                  <h3 className="font-serif font-bold text-charcoal-900 group-hover:text-gold-600 transition-colors duration-300">
                    {product.name}
                  </h3>
                  
                  {/* Rating */}
                  <div className="flex items-center gap-2">
                    <div className="flex text-gold-400">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          size={14}
                          fill={i < Math.floor(product.rating) ? 'currentColor' : 'none'}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-charcoal-600">
                      {product.rating} ({product.reviews} reviews)
                    </span>
                  </div>

                  {/* Colors */}
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-charcoal-600">Colors:</span>
                    <div className="flex gap-1">
                      {product.colors.map((color, index) => (
                        <div
                          key={index}
                          className="w-4 h-4 rounded-full border border-gray-300"
                          style={{ backgroundColor: color }}
                        ></div>
                      ))}
                    </div>
                  </div>

                  {/* Price */}
                  <div className="flex items-center gap-2">
                    <span className="text-xl font-bold text-charcoal-900">
                      ${product.price}
                    </span>
                    <span className="text-sm text-charcoal-500 line-through">
                      ${product.originalPrice}
                    </span>
                    <span className="text-sm text-green-600 font-medium">
                      {Math.round((1 - product.price / product.originalPrice) * 100)}% off
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="luxury-button"
          >
            View All Collections
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default ProductShowcase
