@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&family=Inter:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Inter', system-ui, sans-serif;
    background-color: #fefdfb;
    color: #202124;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .luxury-gradient {
    background: linear-gradient(135deg, #f4a952, #f9a8d4, #f4a952);
  }

  .glass-effect {
    backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .luxury-shadow {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  .luxury-button {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #f4a952, #ec4899);
    color: white;
    font-weight: 500;
    border-radius: 9999px;
    transform: scale(1);
    transition: all 0.3s ease;
  }

  .luxury-button:hover {
    transform: scale(1.05);
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
  }

  .luxury-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transform: scale(1);
    transition: all 0.3s ease;
  }

  .luxury-card:hover {
    transform: scale(1.05);
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
  }

  .section-padding {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  @media (min-width: 640px) {
    .section-padding {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media (min-width: 1024px) {
    .section-padding {
      padding-left: 3rem;
      padding-right: 3rem;
    }
  }

  .container-luxury {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  @media (min-width: 640px) {
    .container-luxury {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media (min-width: 1024px) {
    .container-luxury {
      padding-left: 3rem;
      padding-right: 3rem;
    }
  }
}

@layer utilities {
  .text-gradient {
    background: linear-gradient(to right, #d97706, #ec4899, #d97706);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .animate-float-delayed {
    animation: float 6s ease-in-out infinite 2s;
  }

  .animate-shimmer {
    position: relative;
    overflow: hidden;
  }

  .animate-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
  }
}
