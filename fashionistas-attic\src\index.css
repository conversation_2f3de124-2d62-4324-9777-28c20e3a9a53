@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&family=Inter:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap');
@import "tailwindcss";

@theme {
  /* Luxury color palette */
  --color-gold-50: #fefdf8;
  --color-gold-100: #fef7e0;
  --color-gold-200: #fdecc4;
  --color-gold-300: #fbdc9c;
  --color-gold-400: #f8c572;
  --color-gold-500: #f4a952;
  --color-gold-600: #e88c2d;
  --color-gold-700: #d97706;
  --color-gold-800: #b45309;
  --color-gold-900: #92400e;
  --color-gold-950: #78350f;

  --color-rose-50: #fdf2f8;
  --color-rose-100: #fce7f3;
  --color-rose-200: #fbcfe8;
  --color-rose-300: #f9a8d4;
  --color-rose-400: #f472b6;
  --color-rose-500: #ec4899;
  --color-rose-600: #db2777;
  --color-rose-700: #be185d;
  --color-rose-800: #9d174d;
  --color-rose-900: #831843;
  --color-rose-950: #500724;

  --color-charcoal-50: #f8f9fa;
  --color-charcoal-100: #f1f3f4;
  --color-charcoal-200: #e8eaed;
  --color-charcoal-300: #dadce0;
  --color-charcoal-400: #bdc1c6;
  --color-charcoal-500: #9aa0a6;
  --color-charcoal-600: #80868b;
  --color-charcoal-700: #5f6368;
  --color-charcoal-800: #3c4043;
  --color-charcoal-900: #202124;
  --color-charcoal-950: #0d0e0f;

  --color-cream-50: #fefdfb;
  --color-cream-100: #fef9f3;
  --color-cream-200: #fdf2e9;
  --color-cream-300: #fae8d4;
  --color-cream-400: #f6d8b8;
  --color-cream-500: #f0c394;
  --color-cream-600: #e7a96e;
  --color-cream-700: #dc8b4c;
  --color-cream-800: #c4713a;
  --color-cream-900: #a05d31;
  --color-cream-950: #5a3018;

  /* Typography */
  --font-family-serif: "Playfair Display", serif;
  --font-family-sans: "Inter", system-ui, sans-serif;
  --font-family-script: "Dancing Script", cursive;

  /* Font sizes */
  --font-size-hero: 4.5rem;
  --font-size-display: 3.5rem;
  --font-size-headline: 2.5rem;

  /* Spacing */
  --spacing-18: 4.5rem;
  --spacing-88: 22rem;
  --spacing-128: 32rem;

  /* Animations */
  --animate-fade-in: fadeIn 0.8s ease-out;
  --animate-slide-up: slideUp 0.8s ease-out;
  --animate-float: float 6s ease-in-out infinite;
  --animate-shimmer: shimmer 2s linear infinite;

  /* Shadows */
  --shadow-luxury: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-luxury-lg: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
}

/* Keyframe animations */
@keyframes fadeIn {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  0% { opacity: 0; transform: translateY(40px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Luxury depth and background effects */
.bg-radial-gradient {
  background: radial-gradient(circle at center, var(--tw-gradient-stops));
}

/* Enhanced backdrop blur for luxury feel */
.backdrop-blur-luxury {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
}

/* Depth shadow effects */
.shadow-depth {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-depth-lg {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

@layer base {
  body {
    font-family: 'Inter', system-ui, sans-serif;
    background-color: #fefdfb;
    color: #202124;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .luxury-gradient {
    background: linear-gradient(135deg, #78350f, #92400e, #78350f);
  }

  .luxury-gradient-elegant {
    background: linear-gradient(135deg, #0d0e0f, #3c4043, #0d0e0f);
  }

  .glass-effect {
    backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .luxury-shadow {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  .luxury-button {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #78350f, #92400e);
    color: white;
    font-weight: 500;
    border-radius: 9999px;
    transform: scale(1);
    transition: all 0.3s ease;
  }

  .luxury-button:hover {
    transform: scale(1.05);
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
  }

  .luxury-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transform: scale(1);
    transition: all 0.3s ease;
  }

  .luxury-card:hover {
    transform: scale(1.05);
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
  }

  .section-padding {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  @media (min-width: 640px) {
    .section-padding {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media (min-width: 1024px) {
    .section-padding {
      padding-left: 3rem;
      padding-right: 3rem;
    }
  }

  .container-luxury {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  @media (min-width: 640px) {
    .container-luxury {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media (min-width: 1024px) {
    .container-luxury {
      padding-left: 3rem;
      padding-right: 3rem;
    }
  }
}

@layer utilities {
  .text-gradient {
    background: linear-gradient(to right, #78350f, #92400e, #78350f);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .text-gradient-elegant {
    background: linear-gradient(to right, #3c4043, #5f6368, #3c4043);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .animate-float-delayed {
    animation: float 6s ease-in-out infinite 2s;
  }

  .animate-shimmer {
    position: relative;
    overflow: hidden;
  }

  .animate-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
  }
}
