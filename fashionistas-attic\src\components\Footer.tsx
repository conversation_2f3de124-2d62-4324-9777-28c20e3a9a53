import { motion } from 'framer-motion'
import { 
  Facebook, 
  Instagram, 
  Twitter, 
  Mail, 
  Phone, 
  MapPin, 
  Heart,
  ArrowRight,
  Sparkles
} from 'lucide-react'

const Footer = () => {
  const footerLinks = {
    shop: [
      { name: 'Women\'s Fashion', href: '#' },
      { name: 'Children\'s Wear', href: '#' },
      { name: 'Accessories', href: '#' },
      { name: 'New Arrivals', href: '#' },
      { name: 'Sale Items', href: '#' },
    ],
    events: [
      { name: 'Upcoming Events', href: '#events' },
      { name: 'Event Calendar', href: '#' },
      { name: 'Private Shopping', href: '#' },
      { name: 'Group Bookings', href: '#' },
      { name: 'Event Gallery', href: '#' },
    ],
    support: [
      { name: 'Contact Us', href: '#contact' },
      { name: 'Size Guide', href: '#' },
      { name: 'Return Policy', href: '#' },
      { name: 'Shipping Info', href: '#' },
      { name: 'FAQ', href: '#' },
    ],
    company: [
      { name: 'Our Story', href: '#story' },
      { name: 'Mission & Values', href: '#' },
      { name: 'Sustainability', href: '#' },
      { name: 'Press Kit', href: '#' },
      { name: 'Careers', href: '#' },
    ],
  }

  const socialLinks = [
    { icon: Facebook, href: '#', label: 'Facebook' },
    { icon: Instagram, href: '#', label: 'Instagram' },
    { icon: Twitter, href: '#', label: 'Twitter' },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  }

  return (
    <footer id="contact" className="bg-charcoal-900 text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-charcoal-900 via-charcoal-800 to-charcoal-900"></div>
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 100, repeat: Infinity, ease: 'linear' }}
        className="absolute top-20 right-20 w-64 h-64 border border-gold-800 rounded-full opacity-10"
      ></motion.div>

      <div className="container-luxury relative z-10">
        {/* Newsletter Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="py-16 border-b border-charcoal-700"
        >
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Sparkles className="text-gold-400" size={24} />
              <span className="text-gold-400 font-medium uppercase tracking-wider text-sm">
                Stay Connected
              </span>
              <Sparkles className="text-gold-400" size={24} />
            </div>
            
            <h3 className="text-2xl sm:text-3xl lg:text-4xl font-serif font-bold mb-4">
              Join Our Fashion{' '}
              <span className="text-gradient">Community</span>
            </h3>
            
            <p className="text-charcoal-300 mb-8 max-w-2xl mx-auto">
              Be the first to know about new collections, exclusive pop-up events, 
              and special offers. Plus, get 15% off your first purchase!
            </p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-6 py-4 bg-charcoal-800 border border-charcoal-600 rounded-full 
                         text-white placeholder-charcoal-400 focus:outline-none focus:ring-2 
                         focus:ring-gold-400 focus:border-transparent transition-all duration-300"
              />
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-4 bg-gradient-luxury from-gold-500 to-rose-500 text-white 
                         font-medium rounded-full hover:shadow-luxury transition-all duration-300 
                         flex items-center justify-center gap-2 group"
              >
                Subscribe
                <ArrowRight size={16} className="group-hover:translate-x-1 transition-transform duration-300" />
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Main Footer Content */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="py-16"
        >
          <div className="grid grid-cols-1 lg:grid-cols-6 gap-12">
            {/* Brand Section */}
            <motion.div variants={itemVariants} className="lg:col-span-2">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-12 h-12 bg-gradient-luxury from-gold-500 to-rose-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-xl">FA</span>
                </div>
                <div>
                  <h3 className="text-xl font-serif font-bold">Fashionistas Attic</h3>
                  <p className="text-gold-400 text-sm font-script">Boutique</p>
                </div>
              </div>
              
              <p className="text-charcoal-300 mb-6 leading-relaxed">
                Premium fashion that doesn't break the bank. Join our community of style-conscious 
                shoppers who refuse to compromise on quality.
              </p>

              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-charcoal-300">
                  <Mail size={16} className="text-gold-400" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-charcoal-300">
                  <Phone size={16} className="text-gold-400" />
                  <span>(*************</span>
                </div>
                <div className="flex items-center gap-3 text-charcoal-300">
                  <MapPin size={16} className="text-gold-400" />
                  <span>Various Pop-Up Locations</span>
                </div>
              </div>
            </motion.div>

            {/* Links Sections */}
            <motion.div variants={itemVariants}>
              <h4 className="text-lg font-serif font-bold mb-6 text-gold-400">Shop</h4>
              <ul className="space-y-3">
                {footerLinks.shop.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-charcoal-300 hover:text-gold-400 transition-colors duration-300 text-sm"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>

            <motion.div variants={itemVariants}>
              <h4 className="text-lg font-serif font-bold mb-6 text-gold-400">Events</h4>
              <ul className="space-y-3">
                {footerLinks.events.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-charcoal-300 hover:text-gold-400 transition-colors duration-300 text-sm"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>

            <motion.div variants={itemVariants}>
              <h4 className="text-lg font-serif font-bold mb-6 text-gold-400">Support</h4>
              <ul className="space-y-3">
                {footerLinks.support.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-charcoal-300 hover:text-gold-400 transition-colors duration-300 text-sm"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>

            <motion.div variants={itemVariants}>
              <h4 className="text-lg font-serif font-bold mb-6 text-gold-400">Company</h4>
              <ul className="space-y-3">
                {footerLinks.company.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-charcoal-300 hover:text-gold-400 transition-colors duration-300 text-sm"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>
        </motion.div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="py-8 border-t border-charcoal-700"
        >
          <div className="flex flex-col sm:flex-row justify-between items-center gap-6">
            {/* Copyright */}
            <div className="flex items-center gap-2 text-charcoal-400 text-sm">
              <span>© 2024 Fashionistas Attic Boutique. Made with</span>
              <Heart size={14} className="text-rose-400 fill-current" />
              <span>by Lashonda</span>
            </div>

            {/* Social Links */}
            <div className="flex items-center gap-4">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.label}
                  href={social.href}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-10 h-10 bg-charcoal-800 rounded-full flex items-center justify-center 
                           text-charcoal-400 hover:text-gold-400 hover:bg-charcoal-700 
                           transition-all duration-300"
                  aria-label={social.label}
                >
                  <social.icon size={18} />
                </motion.a>
              ))}
            </div>

            {/* Legal Links */}
            <div className="flex items-center gap-6 text-charcoal-400 text-sm">
              <a href="#" className="hover:text-gold-400 transition-colors duration-300">
                Privacy Policy
              </a>
              <a href="#" className="hover:text-gold-400 transition-colors duration-300">
                Terms of Service
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}

export default Footer
