import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react'

const Hero = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: 'easeOut',
      },
    },
  }

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      rotate: [0, 5, 0],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  }

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gold-400 via-gold-500 to-gold-600">
      {/* Dynamic Fashion Image Layers */}
      <div className="absolute inset-0">
        {/* Main fashion image - positioned like the inspiration */}
        <motion.div
          initial={{ scale: 1.1, opacity: 0 }}
          animate={{
            scale: 1,
            opacity: 1,
            y: [-5, 5, -5]
          }}
          transition={{
            duration: 1.2,
            ease: "easeOut",
            y: {
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[400px] lg:w-[800px] lg:h-[500px]"
        >
          <div className="relative w-full h-full rounded-3xl overflow-hidden shadow-2xl transform rotate-3 hover:rotate-1 transition-transform duration-700">
            <img
              src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800&h=500&fit=crop&crop=center&auto=format&q=80"
              alt="Luxury Fashion"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          </div>
        </motion.div>

        {/* Secondary floating image */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0, x: 100 }}
          animate={{
            scale: 1,
            opacity: 1,
            x: 0,
            y: [-3, 3, -3]
          }}
          transition={{
            duration: 1,
            delay: 0.3,
            ease: "easeOut",
            y: {
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }
          }}
          className="absolute top-20 right-10 w-[200px] h-[250px] lg:w-[280px] lg:h-[350px] hidden md:block"
        >
          <div className="relative w-full h-full rounded-2xl overflow-hidden shadow-xl transform -rotate-12 hover:-rotate-6 transition-transform duration-500">
            <img
              src="https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=280&h=350&fit=crop&crop=center&auto=format&q=80"
              alt="Boutique Fashion"
              className="w-full h-full object-cover"
            />
          </div>
        </motion.div>

        {/* Third accent image */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0, x: -100 }}
          animate={{
            scale: 1,
            opacity: 1,
            x: 0,
            y: [3, -3, 3]
          }}
          transition={{
            duration: 1,
            delay: 0.6,
            ease: "easeOut",
            y: {
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }
          }}
          className="absolute bottom-20 left-10 w-[150px] h-[200px] lg:w-[200px] lg:h-[250px] hidden lg:block"
        >
          <div className="relative w-full h-full rounded-2xl overflow-hidden shadow-xl transform rotate-12 hover:rotate-6 transition-transform duration-500">
            <img
              src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=200&h=250&fit=crop&crop=center&auto=format&q=80"
              alt="Children's Fashion"
              className="w-full h-full object-cover"
            />
          </div>
        </motion.div>
      </div>

      {/* Main Content */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="container-luxury relative z-20 text-left lg:text-left"
      >
        {/* Left side content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6">


            <motion.h1
              variants={itemVariants}
              className="text-5xl sm:text-6xl lg:text-7xl font-serif font-bold text-white mb-6 leading-tight"
            >
              boutique is the
              <br />
              <span className="text-cream-100 italic">new luxury</span>
            </motion.h1>

            <motion.div variants={itemVariants} className="mb-8">
              <p className="text-xl text-white/90 leading-relaxed font-medium max-w-lg">
                Discover curated collections where <span className="text-cream-100 font-semibold">premium quality</span> meets
                accessible pricing. Fashion that rivals luxury brands, designed for real life.
              </p>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="flex flex-col sm:flex-row gap-4"
            >
              <motion.button
                whileHover={{ scale: 1.05, boxShadow: '0 25px 50px rgba(0,0,0,0.3)' }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-4 bg-white text-gold-700 font-semibold rounded-full shadow-xl hover:bg-cream-50 transition-all duration-300"
              >
                Shop Collections
                <ArrowRight className="ml-2 inline" size={20} />
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-4 border-2 border-white text-white font-medium rounded-full hover:bg-white hover:text-gold-700 transition-all duration-300"
              >
                View Pop-Up Events
              </motion.button>
            </motion.div>
          </div>

          {/* Right side - space for the floating images */}
          <div className="hidden lg:block relative h-96">
            {/* This space is for the floating images positioned absolutely */}
          </div>
        </div>



        {/* Trust Indicators */}
        <motion.div
          variants={itemVariants}
          className="flex flex-wrap gap-8 text-white/80 mt-8"
        >
          <div className="flex items-center gap-2">
            <div className="flex text-cream-200">
              {[...Array(5)].map((_, i) => (
                <Star key={i} size={16} fill="currentColor" />
              ))}
            </div>
            <span className="text-sm font-medium">500+ Happy Customers</span>
          </div>
          <div className="flex items-center gap-2">
            <Sparkles className="text-cream-200" size={16} />
            <span className="text-sm font-medium">Premium Quality Guaranteed</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-cream-200 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium">New Arrivals Weekly</span>
          </div>
        </motion.div>
      </motion.div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 2 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-white/60 rounded-full flex justify-center backdrop-blur-sm"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-white/80 rounded-full mt-2"
          ></motion.div>
        </motion.div>
      </motion.div>

      {/* Decorative Elements */}
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 1.5, duration: 0.8 }}
        className="absolute top-1/4 left-1/4 w-20 h-20 border-2 border-white/20 rounded-full hidden lg:block"
      />
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 1.8, duration: 0.8 }}
        className="absolute bottom-1/4 right-1/4 w-16 h-16 border-2 border-white/20 rounded-full hidden lg:block"
      />

      {/* Floating Fashion Icons */}
      <motion.div
        animate={{
          y: [-10, 10, -10],
          rotate: [0, 5, -5, 0]
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="absolute top-32 right-1/4 text-white/30 hidden lg:block"
      >
        <Sparkles size={24} />
      </motion.div>

      <motion.div
        animate={{
          y: [10, -10, 10],
          rotate: [0, -5, 5, 0]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
        className="absolute bottom-32 left-1/3 text-white/30 hidden lg:block"
      >
        <Star size={20} />
      </motion.div>
    </section>
  )
}

export default Hero
