import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react'

const Hero = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: 'easeOut',
      },
    },
  }

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      rotate: [0, 5, 0],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  }

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Luxury Fashion Background with Depth */}
      <div className="absolute inset-0">
        {/* Primary luxury fashion background */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-35"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80')`
          }}
        />
        {/* Secondary depth layer - elegant fashion store */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20 mix-blend-soft-light"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1483985988355-763728e1935b?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80')`
          }}
        />
        {/* Gradient overlay for readability and luxury feel */}
        <div className="absolute inset-0 bg-gradient-to-br from-cream-100/75 via-cream-50/70 to-pearl-100/75" />
        {/* Subtle radial gradient for focus */}
        <div className="absolute inset-0 bg-radial-gradient from-transparent via-transparent to-cream-100/20" />
      </div>
      
      {/* Minimal decorative elements */}

      {/* Main Content */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="container-luxury text-center relative z-10"
      >
        <motion.div variants={itemVariants} className="mb-8">
          <span className="inline-block px-8 py-3 bg-charcoal-900 text-cream-50 text-sm font-medium rounded-full shadow-depth backdrop-blur-luxury border border-charcoal-700">
            ✨ Premium Boutique Fashion
          </span>
        </motion.div>

        <motion.h1
          variants={itemVariants}
          className="text-4xl sm:text-5xl lg:text-hero font-serif font-bold text-charcoal-900 mb-6 leading-tight"
        >
          Where{' '}
          <span className="text-gradient">Luxury</span>
          <br />
          Meets{' '}
          <span className="text-gradient">Affordability</span>
        </motion.h1>

        <motion.div variants={itemVariants} className="mb-10">
          <div className="bg-white/30 backdrop-blur-luxury rounded-2xl p-8 shadow-depth border border-white/20 max-w-3xl mx-auto">
            <p className="text-lg sm:text-xl text-charcoal-700 leading-relaxed font-medium">
              Discover curated collections of <span className="text-gold-700 font-semibold">premium fashion</span> for women and children.
              Quality pieces that rival high-end brands, <span className="text-gold-700 font-semibold">without the luxury price tag</span>.
            </p>
          </div>
        </motion.div>

        <motion.div
          variants={itemVariants}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
        >
          <motion.button
            whileHover={{ scale: 1.05, boxShadow: '0 25px 50px rgba(0,0,0,0.2)' }}
            whileTap={{ scale: 0.95 }}
            className="px-8 py-4 bg-charcoal-900 text-cream-50 font-medium rounded-full shadow-depth-lg backdrop-blur-luxury border border-charcoal-700 group hover:bg-charcoal-800 transition-all duration-300"
          >
            Shop Collections
            <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform duration-300" size={20} />
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-8 py-4 border-2 border-gold-600 text-gold-700 font-medium rounded-full
                     hover:bg-gold-600 hover:text-white transition-all duration-300 shadow-depth backdrop-blur-luxury bg-white/20"
          >
            View Pop-Up Events
          </motion.button>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          variants={itemVariants}
          className="flex flex-wrap justify-center items-center gap-8 text-charcoal-600"
        >
          <div className="flex items-center gap-2">
            <div className="flex text-gold-400">
              {[...Array(5)].map((_, i) => (
                <Star key={i} size={16} fill="currentColor" />
              ))}
            </div>
            <span className="text-sm font-medium">500+ Happy Customers</span>
          </div>
          <div className="flex items-center gap-2">
            <Sparkles className="text-gold-500" size={16} />
            <span className="text-sm font-medium">Premium Quality Guaranteed</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium">New Arrivals Weekly</span>
          </div>
        </motion.div>
      </motion.div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-gold-400 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-gold-400 rounded-full mt-2"
          ></motion.div>
        </motion.div>
      </motion.div>
    </section>
  )
}

export default Hero
