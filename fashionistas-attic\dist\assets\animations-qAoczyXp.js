var Pr=Object.defineProperty,Ar=Object.defineProperties;var Vr=Object.getOwnPropertyDescriptors;var Ut=Object.getOwnPropertySymbols;var Vi=Object.prototype.hasOwnProperty,Mi=Object.prototype.propertyIsEnumerable;var le=Math.pow,ae=(t,e,i)=>e in t?Pr(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i,T=(t,e)=>{for(var i in e||(e={}))Vi.call(e,i)&&ae(t,i,e[i]);if(Ut)for(var i of Ut(e))Mi.call(e,i)&&ae(t,i,e[i]);return t},C=(t,e)=>Ar(t,Vr(e));var z=(t,e)=>{var i={};for(var s in t)Vi.call(t,s)&&e.indexOf(s)<0&&(i[s]=t[s]);if(t!=null&&Ut)for(var s of Ut(t))e.indexOf(s)<0&&Mi.call(t,s)&&(i[s]=t[s]);return i};var b=(t,e,i)=>ae(t,typeof e!="symbol"?e+"":e,i);import{r as Mr}from"./vendor-rxzC6AyD.js";var ce={exports:{}},wt={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ci;function Cr(){if(Ci)return wt;Ci=1;var t=Symbol.for("react.transitional.element"),e=Symbol.for("react.fragment");function i(s,n,o){var r=null;if(o!==void 0&&(r=""+o),n.key!==void 0&&(r=""+n.key),"key"in n){o={};for(var a in n)a!=="key"&&(o[a]=n[a])}else o=n;return n=o.ref,{$$typeof:t,type:s,key:r,ref:n!==void 0?n:null,props:o}}return wt.Fragment=e,wt.jsx=i,wt.jsxs=i,wt}var Ei;function Er(){return Ei||(Ei=1,ce.exports=Cr()),ce.exports}var Z=Er(),w=Mr(),Dr="1.3.4";function Hs(t,e,i){return Math.max(t,Math.min(e,i))}function Rr(t,e,i){return(1-i)*t+i*e}function Lr(t,e,i,s){return Rr(t,e,1-Math.exp(-i*s))}function kr(t,e){return(t%e+e)%e}var Fr=class{constructor(){b(this,"isRunning",!1);b(this,"value",0);b(this,"from",0);b(this,"to",0);b(this,"currentTime",0);b(this,"lerp");b(this,"duration");b(this,"easing");b(this,"onUpdate")}advance(t){var i;if(!this.isRunning)return;let e=!1;if(this.duration&&this.easing){this.currentTime+=t;const s=Hs(0,this.currentTime/this.duration,1);e=s>=1;const n=e?1:this.easing(s);this.value=this.from+(this.to-this.from)*n}else this.lerp?(this.value=Lr(this.value,this.to,this.lerp*60,t),Math.round(this.value)===this.to&&(this.value=this.to,e=!0)):(this.value=this.to,e=!0);e&&this.stop(),(i=this.onUpdate)==null||i.call(this,this.value,e)}stop(){this.isRunning=!1}fromTo(t,e,{lerp:i,duration:s,easing:n,onStart:o,onUpdate:r}){this.from=this.value=t,this.to=e,this.lerp=i,this.duration=s,this.easing=n,this.currentTime=0,this.isRunning=!0,o==null||o(),this.onUpdate=r}};function Or(t,e){let i;return function(...s){let n=this;clearTimeout(i),i=setTimeout(()=>{i=void 0,t.apply(n,s)},e)}}var Br=class{constructor(t,e,{autoResize:i=!0,debounce:s=250}={}){b(this,"width",0);b(this,"height",0);b(this,"scrollHeight",0);b(this,"scrollWidth",0);b(this,"debouncedResize");b(this,"wrapperResizeObserver");b(this,"contentResizeObserver");b(this,"resize",()=>{this.onWrapperResize(),this.onContentResize()});b(this,"onWrapperResize",()=>{this.wrapper instanceof Window?(this.width=window.innerWidth,this.height=window.innerHeight):(this.width=this.wrapper.clientWidth,this.height=this.wrapper.clientHeight)});b(this,"onContentResize",()=>{this.wrapper instanceof Window?(this.scrollHeight=this.content.scrollHeight,this.scrollWidth=this.content.scrollWidth):(this.scrollHeight=this.wrapper.scrollHeight,this.scrollWidth=this.wrapper.scrollWidth)});this.wrapper=t,this.content=e,i&&(this.debouncedResize=Or(this.resize,s),this.wrapper instanceof Window?window.addEventListener("resize",this.debouncedResize,!1):(this.wrapperResizeObserver=new ResizeObserver(this.debouncedResize),this.wrapperResizeObserver.observe(this.wrapper)),this.contentResizeObserver=new ResizeObserver(this.debouncedResize),this.contentResizeObserver.observe(this.content)),this.resize()}destroy(){var t,e;(t=this.wrapperResizeObserver)==null||t.disconnect(),(e=this.contentResizeObserver)==null||e.disconnect(),this.wrapper===window&&this.debouncedResize&&window.removeEventListener("resize",this.debouncedResize,!1)}get limit(){return{x:this.scrollWidth-this.width,y:this.scrollHeight-this.height}}},$s=class{constructor(){b(this,"events",{})}emit(t,...e){var s;let i=this.events[t]||[];for(let n=0,o=i.length;n<o;n++)(s=i[n])==null||s.call(i,...e)}on(t,e){var i;return(i=this.events[t])!=null&&i.push(e)||(this.events[t]=[e]),()=>{var s;this.events[t]=(s=this.events[t])==null?void 0:s.filter(n=>e!==n)}}off(t,e){var i;this.events[t]=(i=this.events[t])==null?void 0:i.filter(s=>e!==s)}destroy(){this.events={}}},Di=100/6,tt={passive:!1},Ir=class{constructor(t,e={wheelMultiplier:1,touchMultiplier:1}){b(this,"touchStart",{x:0,y:0});b(this,"lastDelta",{x:0,y:0});b(this,"window",{width:0,height:0});b(this,"emitter",new $s);b(this,"onTouchStart",t=>{const{clientX:e,clientY:i}=t.targetTouches?t.targetTouches[0]:t;this.touchStart.x=e,this.touchStart.y=i,this.lastDelta={x:0,y:0},this.emitter.emit("scroll",{deltaX:0,deltaY:0,event:t})});b(this,"onTouchMove",t=>{const{clientX:e,clientY:i}=t.targetTouches?t.targetTouches[0]:t,s=-(e-this.touchStart.x)*this.options.touchMultiplier,n=-(i-this.touchStart.y)*this.options.touchMultiplier;this.touchStart.x=e,this.touchStart.y=i,this.lastDelta={x:s,y:n},this.emitter.emit("scroll",{deltaX:s,deltaY:n,event:t})});b(this,"onTouchEnd",t=>{this.emitter.emit("scroll",{deltaX:this.lastDelta.x,deltaY:this.lastDelta.y,event:t})});b(this,"onWheel",t=>{let{deltaX:e,deltaY:i,deltaMode:s}=t;const n=s===1?Di:s===2?this.window.width:1,o=s===1?Di:s===2?this.window.height:1;e*=n,i*=o,e*=this.options.wheelMultiplier,i*=this.options.wheelMultiplier,this.emitter.emit("scroll",{deltaX:e,deltaY:i,event:t})});b(this,"onWindowResize",()=>{this.window={width:window.innerWidth,height:window.innerHeight}});this.element=t,this.options=e,window.addEventListener("resize",this.onWindowResize,!1),this.onWindowResize(),this.element.addEventListener("wheel",this.onWheel,tt),this.element.addEventListener("touchstart",this.onTouchStart,tt),this.element.addEventListener("touchmove",this.onTouchMove,tt),this.element.addEventListener("touchend",this.onTouchEnd,tt)}on(t,e){return this.emitter.on(t,e)}destroy(){this.emitter.destroy(),window.removeEventListener("resize",this.onWindowResize,!1),this.element.removeEventListener("wheel",this.onWheel,tt),this.element.removeEventListener("touchstart",this.onTouchStart,tt),this.element.removeEventListener("touchmove",this.onTouchMove,tt),this.element.removeEventListener("touchend",this.onTouchEnd,tt)}},Ri=t=>Math.min(1,1.001-Math.pow(2,-10*t)),Vu=class{constructor({wrapper:t=window,content:e=document.documentElement,eventsTarget:i=t,smoothWheel:s=!0,syncTouch:n=!1,syncTouchLerp:o=.075,touchInertiaMultiplier:r=35,duration:a,easing:l,lerp:u=.1,infinite:c=!1,orientation:h="vertical",gestureOrientation:f="vertical",touchMultiplier:d=1,wheelMultiplier:p=1,autoResize:m=!0,prevent:g,virtualScroll:y,overscroll:S=!0,autoRaf:v=!1,anchors:V=!1,autoToggle:x=!1,allowNestedScroll:A=!1,__experimental__naiveDimensions:E=!1}={}){b(this,"_isScrolling",!1);b(this,"_isStopped",!1);b(this,"_isLocked",!1);b(this,"_preventNextNativeScrollEvent",!1);b(this,"_resetVelocityTimeout",null);b(this,"__rafID",null);b(this,"isTouching");b(this,"time",0);b(this,"userData",{});b(this,"lastVelocity",0);b(this,"velocity",0);b(this,"direction",0);b(this,"options");b(this,"targetScroll");b(this,"animatedScroll");b(this,"animate",new Fr);b(this,"emitter",new $s);b(this,"dimensions");b(this,"virtualScroll");b(this,"onScrollEnd",t=>{t instanceof CustomEvent||(this.isScrolling==="smooth"||this.isScrolling===!1)&&t.stopPropagation()});b(this,"dispatchScrollendEvent",()=>{this.options.wrapper.dispatchEvent(new CustomEvent("scrollend",{bubbles:this.options.wrapper===window,detail:{lenisScrollEnd:!0}}))});b(this,"onTransitionEnd",t=>{if(t.propertyName.includes("overflow")){const e=this.isHorizontal?"overflow-x":"overflow-y",i=getComputedStyle(this.rootElement)[e];["hidden","clip"].includes(i)?this.stop():this.start()}});b(this,"onClick",t=>{const i=t.composedPath().find(s=>{var n,o,r;return s instanceof HTMLAnchorElement&&(((n=s.getAttribute("href"))==null?void 0:n.startsWith("#"))||((o=s.getAttribute("href"))==null?void 0:o.startsWith("/#"))||((r=s.getAttribute("href"))==null?void 0:r.startsWith("./#")))});if(i){const s=i.getAttribute("href");if(s){const n=typeof this.options.anchors=="object"&&this.options.anchors?this.options.anchors:void 0;let o=`#${s.split("#")[1]}`;["#","/#","./#","#top","/#top","./#top"].includes(s)&&(o=0),this.scrollTo(o,n)}}});b(this,"onPointerDown",t=>{t.button===1&&this.reset()});b(this,"onVirtualScroll",t=>{if(typeof this.options.virtualScroll=="function"&&this.options.virtualScroll(t)===!1)return;const{deltaX:e,deltaY:i,event:s}=t;if(this.emitter.emit("virtual-scroll",{deltaX:e,deltaY:i,event:s}),s.ctrlKey||s.lenisStopPropagation)return;const n=s.type.includes("touch"),o=s.type.includes("wheel");this.isTouching=s.type==="touchstart"||s.type==="touchmove";const r=e===0&&i===0;if(this.options.syncTouch&&n&&s.type==="touchstart"&&r&&!this.isStopped&&!this.isLocked){this.reset();return}const l=this.options.gestureOrientation==="vertical"&&i===0||this.options.gestureOrientation==="horizontal"&&e===0;if(r||l)return;let u=s.composedPath();u=u.slice(0,u.indexOf(this.rootElement));const c=this.options.prevent;if(u.find(g=>{var y,S,v;return g instanceof HTMLElement&&(typeof c=="function"&&(c==null?void 0:c(g))||((y=g.hasAttribute)==null?void 0:y.call(g,"data-lenis-prevent"))||n&&((S=g.hasAttribute)==null?void 0:S.call(g,"data-lenis-prevent-touch"))||o&&((v=g.hasAttribute)==null?void 0:v.call(g,"data-lenis-prevent-wheel"))||this.options.allowNestedScroll&&this.checkNestedScroll(g,{deltaX:e,deltaY:i}))}))return;if(this.isStopped||this.isLocked){s.preventDefault();return}if(!(this.options.syncTouch&&n||this.options.smoothWheel&&o)){this.isScrolling="native",this.animate.stop(),s.lenisStopPropagation=!0;return}let f=i;this.options.gestureOrientation==="both"?f=Math.abs(i)>Math.abs(e)?i:e:this.options.gestureOrientation==="horizontal"&&(f=e),(!this.options.overscroll||this.options.infinite||this.options.wrapper!==window&&(this.animatedScroll>0&&this.animatedScroll<this.limit||this.animatedScroll===0&&i>0||this.animatedScroll===this.limit&&i<0))&&(s.lenisStopPropagation=!0),s.preventDefault();const d=n&&this.options.syncTouch,m=n&&s.type==="touchend"&&Math.abs(f)>5;m&&(f=this.velocity*this.options.touchInertiaMultiplier),this.scrollTo(this.targetScroll+f,T({programmatic:!1},d?{lerp:m?this.options.syncTouchLerp:1}:{lerp:this.options.lerp,duration:this.options.duration,easing:this.options.easing}))});b(this,"onNativeScroll",()=>{if(this._resetVelocityTimeout!==null&&(clearTimeout(this._resetVelocityTimeout),this._resetVelocityTimeout=null),this._preventNextNativeScrollEvent){this._preventNextNativeScrollEvent=!1;return}if(this.isScrolling===!1||this.isScrolling==="native"){const t=this.animatedScroll;this.animatedScroll=this.targetScroll=this.actualScroll,this.lastVelocity=this.velocity,this.velocity=this.animatedScroll-t,this.direction=Math.sign(this.animatedScroll-t),this.isStopped||(this.isScrolling="native"),this.emit(),this.velocity!==0&&(this._resetVelocityTimeout=setTimeout(()=>{this.lastVelocity=this.velocity,this.velocity=0,this.isScrolling=!1,this.emit()},400))}});b(this,"raf",t=>{const e=t-(this.time||t);this.time=t,this.animate.advance(e*.001),this.options.autoRaf&&(this.__rafID=requestAnimationFrame(this.raf))});window.lenisVersion=Dr,(!t||t===document.documentElement)&&(t=window),typeof a=="number"&&typeof l!="function"?l=Ri:typeof l=="function"&&typeof a!="number"&&(a=1),this.options={wrapper:t,content:e,eventsTarget:i,smoothWheel:s,syncTouch:n,syncTouchLerp:o,touchInertiaMultiplier:r,duration:a,easing:l,lerp:u,infinite:c,gestureOrientation:f,orientation:h,touchMultiplier:d,wheelMultiplier:p,autoResize:m,prevent:g,virtualScroll:y,overscroll:S,autoRaf:v,anchors:V,autoToggle:x,allowNestedScroll:A,__experimental__naiveDimensions:E},this.dimensions=new Br(t,e,{autoResize:m}),this.updateClassName(),this.targetScroll=this.animatedScroll=this.actualScroll,this.options.wrapper.addEventListener("scroll",this.onNativeScroll,!1),this.options.wrapper.addEventListener("scrollend",this.onScrollEnd,{capture:!0}),this.options.anchors&&this.options.wrapper===window&&this.options.wrapper.addEventListener("click",this.onClick,!1),this.options.wrapper.addEventListener("pointerdown",this.onPointerDown,!1),this.virtualScroll=new Ir(i,{touchMultiplier:d,wheelMultiplier:p}),this.virtualScroll.on("scroll",this.onVirtualScroll),this.options.autoToggle&&this.rootElement.addEventListener("transitionend",this.onTransitionEnd,{passive:!0}),this.options.autoRaf&&(this.__rafID=requestAnimationFrame(this.raf))}destroy(){this.emitter.destroy(),this.options.wrapper.removeEventListener("scroll",this.onNativeScroll,!1),this.options.wrapper.removeEventListener("scrollend",this.onScrollEnd,{capture:!0}),this.options.wrapper.removeEventListener("pointerdown",this.onPointerDown,!1),this.options.anchors&&this.options.wrapper===window&&this.options.wrapper.removeEventListener("click",this.onClick,!1),this.virtualScroll.destroy(),this.dimensions.destroy(),this.cleanUpClassName(),this.__rafID&&cancelAnimationFrame(this.__rafID)}on(t,e){return this.emitter.on(t,e)}off(t,e){return this.emitter.off(t,e)}setScroll(t){this.isHorizontal?this.options.wrapper.scrollTo({left:t,behavior:"instant"}):this.options.wrapper.scrollTo({top:t,behavior:"instant"})}resize(){this.dimensions.resize(),this.animatedScroll=this.targetScroll=this.actualScroll,this.emit()}emit(){this.emitter.emit("scroll",this)}reset(){this.isLocked=!1,this.isScrolling=!1,this.animatedScroll=this.targetScroll=this.actualScroll,this.lastVelocity=this.velocity=0,this.animate.stop()}start(){this.isStopped&&(this.reset(),this.isStopped=!1,this.emit())}stop(){this.isStopped||(this.reset(),this.isStopped=!0,this.emit())}scrollTo(t,{offset:e=0,immediate:i=!1,lock:s=!1,duration:n=this.options.duration,easing:o=this.options.easing,lerp:r=this.options.lerp,onStart:a,onComplete:l,force:u=!1,programmatic:c=!0,userData:h}={}){if(!((this.isStopped||this.isLocked)&&!u)){if(typeof t=="string"&&["top","left","start"].includes(t))t=0;else if(typeof t=="string"&&["bottom","right","end"].includes(t))t=this.limit;else{let f;if(typeof t=="string"?f=document.querySelector(t):t instanceof HTMLElement&&(t!=null&&t.nodeType)&&(f=t),f){if(this.options.wrapper!==window){const p=this.rootElement.getBoundingClientRect();e-=this.isHorizontal?p.left:p.top}const d=f.getBoundingClientRect();t=(this.isHorizontal?d.left:d.top)+this.animatedScroll}}if(typeof t=="number"){if(t+=e,t=Math.round(t),this.options.infinite){if(c){this.targetScroll=this.animatedScroll=this.scroll;const f=t-this.animatedScroll;f>this.limit/2?t=t-this.limit:f<-this.limit/2&&(t=t+this.limit)}}else t=Hs(0,t,this.limit);if(t===this.targetScroll){a==null||a(this),l==null||l(this);return}if(this.userData=h!=null?h:{},i){this.animatedScroll=this.targetScroll=t,this.setScroll(this.scroll),this.reset(),this.preventNextNativeScrollEvent(),this.emit(),l==null||l(this),this.userData={},requestAnimationFrame(()=>{this.dispatchScrollendEvent()});return}c||(this.targetScroll=t),typeof n=="number"&&typeof o!="function"?o=Ri:typeof o=="function"&&typeof n!="number"&&(n=1),this.animate.fromTo(this.animatedScroll,t,{duration:n,easing:o,lerp:r,onStart:()=>{s&&(this.isLocked=!0),this.isScrolling="smooth",a==null||a(this)},onUpdate:(f,d)=>{this.isScrolling="smooth",this.lastVelocity=this.velocity,this.velocity=f-this.animatedScroll,this.direction=Math.sign(this.velocity),this.animatedScroll=f,this.setScroll(this.scroll),c&&(this.targetScroll=f),d||this.emit(),d&&(this.reset(),this.emit(),l==null||l(this),this.userData={},requestAnimationFrame(()=>{this.dispatchScrollendEvent()}),this.preventNextNativeScrollEvent())}})}}}preventNextNativeScrollEvent(){this._preventNextNativeScrollEvent=!0,requestAnimationFrame(()=>{this._preventNextNativeScrollEvent=!1})}checkNestedScroll(t,{deltaX:e,deltaY:i}){var x,A;const s=Date.now(),n=(x=t._lenis)!=null?x:t._lenis={};let o,r,a,l,u,c,h,f;const d=this.options.gestureOrientation;if(s-((A=n.time)!=null?A:0)>2e3){n.time=Date.now();const E=window.getComputedStyle(t);n.computedStyle=E;const M=E.overflowX,O=E.overflowY;if(o=["auto","overlay","scroll"].includes(M),r=["auto","overlay","scroll"].includes(O),n.hasOverflowX=o,n.hasOverflowY=r,!o&&!r||d==="vertical"&&!r||d==="horizontal"&&!o)return!1;u=t.scrollWidth,c=t.scrollHeight,h=t.clientWidth,f=t.clientHeight,a=u>h,l=c>f,n.isScrollableX=a,n.isScrollableY=l,n.scrollWidth=u,n.scrollHeight=c,n.clientWidth=h,n.clientHeight=f}else a=n.isScrollableX,l=n.isScrollableY,o=n.hasOverflowX,r=n.hasOverflowY,u=n.scrollWidth,c=n.scrollHeight,h=n.clientWidth,f=n.clientHeight;if(!o&&!r||!a&&!l||d==="vertical"&&(!r||!l)||d==="horizontal"&&(!o||!a))return!1;let p;if(d==="horizontal")p="x";else if(d==="vertical")p="y";else{const E=e!==0,M=i!==0;E&&o&&a&&(p="x"),M&&r&&l&&(p="y")}if(!p)return!1;let m,g,y,S,v;if(p==="x")m=t.scrollLeft,g=u-h,y=e,S=o,v=a;else if(p==="y")m=t.scrollTop,g=c-f,y=i,S=r,v=l;else return!1;return(y>0?m<g:m>0)&&S&&v}get rootElement(){return this.options.wrapper===window?document.documentElement:this.options.wrapper}get limit(){return this.options.__experimental__naiveDimensions?this.isHorizontal?this.rootElement.scrollWidth-this.rootElement.clientWidth:this.rootElement.scrollHeight-this.rootElement.clientHeight:this.dimensions.limit[this.isHorizontal?"x":"y"]}get isHorizontal(){return this.options.orientation==="horizontal"}get actualScroll(){var e,i;const t=this.options.wrapper;return this.isHorizontal?(e=t.scrollX)!=null?e:t.scrollLeft:(i=t.scrollY)!=null?i:t.scrollTop}get scroll(){return this.options.infinite?kr(this.animatedScroll,this.limit):this.animatedScroll}get progress(){return this.limit===0?1:this.scroll/this.limit}get isScrolling(){return this._isScrolling}set isScrolling(t){this._isScrolling!==t&&(this._isScrolling=t,this.updateClassName())}get isStopped(){return this._isStopped}set isStopped(t){this._isStopped!==t&&(this._isStopped=t,this.updateClassName())}get isLocked(){return this._isLocked}set isLocked(t){this._isLocked!==t&&(this._isLocked=t,this.updateClassName())}get isSmooth(){return this.isScrolling==="smooth"}get className(){let t="lenis";return this.options.autoToggle&&(t+=" lenis-autoToggle"),this.isStopped&&(t+=" lenis-stopped"),this.isLocked&&(t+=" lenis-locked"),this.isScrolling&&(t+=" lenis-scrolling"),this.isScrolling==="smooth"&&(t+=" lenis-smooth"),t}updateClassName(){this.cleanUpClassName(),this.rootElement.className=`${this.rootElement.className} ${this.className}`.trim()}cleanUpClassName(){this.rootElement.className=this.rootElement.className.replace(/lenis(-\w+)?/g,"").trim()}};const _e=w.createContext({});function He(t){const e=w.useRef(null);return e.current===null&&(e.current=t()),e.current}const $e=typeof window!="undefined",Ks=$e?w.useLayoutEffect:w.useEffect,ee=w.createContext(null);function Ke(t,e){t.indexOf(e)===-1&&t.push(e)}function Xe(t,e){const i=t.indexOf(e);i>-1&&t.splice(i,1)}const J=(t,e,i)=>i>e?e:i<t?t:i;let Ye=()=>{};const Q={},Xs=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function Ys(t){return typeof t=="object"&&t!==null}const Gs=t=>/^0[^.\s]+$/u.test(t);function Ge(t){let e;return()=>(e===void 0&&(e=t()),e)}const $=t=>t,jr=(t,e)=>i=>e(t(i)),It=(...t)=>t.reduce(jr),Dt=(t,e,i)=>{const s=e-t;return s===0?1:(i-t)/s};class qe{constructor(){this.subscriptions=[]}add(e){return Ke(this.subscriptions,e),()=>Xe(this.subscriptions,e)}notify(e,i,s){const n=this.subscriptions.length;if(n)if(n===1)this.subscriptions[0](e,i,s);else for(let o=0;o<n;o++){const r=this.subscriptions[o];r&&r(e,i,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const X=t=>t*1e3,Y=t=>t/1e3;function qs(t,e){return e?t*(1e3/e):0}const Zs=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t,Nr=1e-7,Wr=12;function Ur(t,e,i,s,n){let o,r,a=0;do r=e+(i-e)/2,o=Zs(r,s,n)-t,o>0?i=r:e=r;while(Math.abs(o)>Nr&&++a<Wr);return r}function jt(t,e,i,s){if(t===e&&i===s)return $;const n=o=>Ur(o,0,1,t,i);return o=>o===0||o===1?o:Zs(n(o),e,s)}const Js=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Qs=t=>e=>1-t(1-e),tn=jt(.33,1.53,.69,.99),Ze=Qs(tn),en=Js(Ze),sn=t=>(t*=2)<1?.5*Ze(t):.5*(2-Math.pow(2,-10*(t-1))),Je=t=>1-Math.sin(Math.acos(t)),nn=Qs(Je),rn=Js(Je),zr=jt(.42,0,1,1),_r=jt(0,0,.58,1),on=jt(.42,0,.58,1),Hr=t=>Array.isArray(t)&&typeof t[0]!="number",an=t=>Array.isArray(t)&&typeof t[0]=="number",$r={linear:$,easeIn:zr,easeInOut:on,easeOut:_r,circIn:Je,circInOut:rn,circOut:nn,backIn:Ze,backInOut:en,backOut:tn,anticipate:sn},Kr=t=>typeof t=="string",Li=t=>{if(an(t)){Ye(t.length===4);const[e,i,s,n]=t;return jt(e,i,s,n)}else if(Kr(t))return $r[t];return t},zt=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ki={value:null};function Xr(t,e){let i=new Set,s=new Set,n=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(h){r.has(h)&&(c.schedule(h),t()),l++,h(a)}const c={schedule:(h,f=!1,d=!1)=>{const m=d&&n?i:s;return f&&r.add(h),m.has(h)||m.add(h),h},cancel:h=>{s.delete(h),r.delete(h)},process:h=>{if(a=h,n){o=!0;return}n=!0,[i,s]=[s,i],i.forEach(u),e&&ki.value&&ki.value.frameloop[e].push(l),l=0,i.clear(),n=!1,o&&(o=!1,c.process(h))}};return c}const Yr=40;function ln(t,e){let i=!1,s=!0;const n={delta:0,timestamp:0,isProcessing:!1},o=()=>i=!0,r=zt.reduce((v,V)=>(v[V]=Xr(o,e?V:void 0),v),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:h,preRender:f,render:d,postRender:p}=r,m=()=>{const v=Q.useManualTiming?n.timestamp:performance.now();i=!1,Q.useManualTiming||(n.delta=s?1e3/60:Math.max(Math.min(v-n.timestamp,Yr),1)),n.timestamp=v,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),c.process(n),h.process(n),f.process(n),d.process(n),p.process(n),n.isProcessing=!1,i&&e&&(s=!1,t(m))},g=()=>{i=!0,s=!0,n.isProcessing||t(m)};return{schedule:zt.reduce((v,V)=>{const x=r[V];return v[V]=(A,E=!1,M=!1)=>(i||g(),x.schedule(A,E,M)),v},{}),cancel:v=>{for(let V=0;V<zt.length;V++)r[zt[V]].cancel(v)},state:n,steps:r}}const{schedule:R,cancel:it,state:B,steps:ue}=ln(typeof requestAnimationFrame!="undefined"?requestAnimationFrame:$,!0);let Kt;function Gr(){Kt=void 0}const U={now:()=>(Kt===void 0&&U.set(B.isProcessing||Q.useManualTiming?B.timestamp:performance.now()),Kt),set:t=>{Kt=t,queueMicrotask(Gr)}},cn=t=>e=>typeof e=="string"&&e.startsWith(t),Qe=cn("--"),qr=cn("var(--"),ti=t=>qr(t)?Zr.test(t.split("/*")[0].trim()):!1,Zr=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Tt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Rt=C(T({},Tt),{transform:t=>J(0,1,t)}),_t=C(T({},Tt),{default:1}),At=t=>Math.round(t*1e5)/1e5,ei=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Jr(t){return t==null}const Qr=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ii=(t,e)=>i=>!!(typeof i=="string"&&Qr.test(i)&&i.startsWith(t)||e&&!Jr(i)&&Object.prototype.hasOwnProperty.call(i,e)),un=(t,e,i)=>s=>{if(typeof s!="string")return s;const[n,o,r,a]=s.match(ei);return{[t]:parseFloat(n),[e]:parseFloat(o),[i]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},to=t=>J(0,255,t),he=C(T({},Tt),{transform:t=>Math.round(to(t))}),at={test:ii("rgb","red"),parse:un("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+he.transform(t)+", "+he.transform(e)+", "+he.transform(i)+", "+At(Rt.transform(s))+")"};function eo(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}}const be={test:ii("#"),parse:eo,transform:at.transform},Nt=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),et=Nt("deg"),G=Nt("%"),P=Nt("px"),io=Nt("vh"),so=Nt("vw"),Fi=C(T({},G),{parse:t=>G.parse(t)/100,transform:t=>G.transform(t*100)}),ft={test:ii("hsl","hue"),parse:un("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+G.transform(At(e))+", "+G.transform(At(i))+", "+At(Rt.transform(s))+")"},F={test:t=>at.test(t)||be.test(t)||ft.test(t),parse:t=>at.test(t)?at.parse(t):ft.test(t)?ft.parse(t):be.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?at.transform(t):ft.transform(t),getAnimatableNone:t=>{const e=F.parse(t);return e.alpha=0,F.transform(e)}},no=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function ro(t){var e,i;return isNaN(t)&&typeof t=="string"&&(((e=t.match(ei))==null?void 0:e.length)||0)+(((i=t.match(no))==null?void 0:i.length)||0)>0}const hn="number",fn="color",oo="var",ao="var(",Oi="${}",lo=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Lt(t){const e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[];let o=0;const a=e.replace(lo,l=>(F.test(l)?(s.color.push(o),n.push(fn),i.push(F.parse(l))):l.startsWith(ao)?(s.var.push(o),n.push(oo),i.push(l)):(s.number.push(o),n.push(hn),i.push(parseFloat(l))),++o,Oi)).split(Oi);return{values:i,split:a,indexes:s,types:n}}function dn(t){return Lt(t).values}function pn(t){const{split:e,types:i}=Lt(t),s=e.length;return n=>{let o="";for(let r=0;r<s;r++)if(o+=e[r],n[r]!==void 0){const a=i[r];a===hn?o+=At(n[r]):a===fn?o+=F.transform(n[r]):o+=n[r]}return o}}const co=t=>typeof t=="number"?0:F.test(t)?F.getAnimatableNone(t):t;function uo(t){const e=dn(t);return pn(t)(e.map(co))}const st={test:ro,parse:dn,createTransformer:pn,getAnimatableNone:uo};function fe(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+(e-t)*6*i:i<1/2?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function ho({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,e/=100,i/=100;let n=0,o=0,r=0;if(!e)n=o=r=i;else{const a=i<.5?i*(1+e):i+e-i*e,l=2*i-a;n=fe(l,a,t+1/3),o=fe(l,a,t),r=fe(l,a,t-1/3)}return{red:Math.round(n*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:s}}function qt(t,e){return i=>i>0?e:t}const D=(t,e,i)=>t+(e-t)*i,de=(t,e,i)=>{const s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},fo=[be,at,ft],po=t=>fo.find(e=>e.test(t));function Bi(t){const e=po(t);if(!e)return!1;let i=e.parse(t);return e===ft&&(i=ho(i)),i}const Ii=(t,e)=>{const i=Bi(t),s=Bi(e);if(!i||!s)return qt(t,e);const n=T({},i);return o=>(n.red=de(i.red,s.red,o),n.green=de(i.green,s.green,o),n.blue=de(i.blue,s.blue,o),n.alpha=D(i.alpha,s.alpha,o),at.transform(n))},Pe=new Set(["none","hidden"]);function mo(t,e){return Pe.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}function go(t,e){return i=>D(t,e,i)}function si(t){return typeof t=="number"?go:typeof t=="string"?ti(t)?qt:F.test(t)?Ii:To:Array.isArray(t)?mn:typeof t=="object"?F.test(t)?Ii:yo:qt}function mn(t,e){const i=[...t],s=i.length,n=t.map((o,r)=>si(o)(o,e[r]));return o=>{for(let r=0;r<s;r++)i[r]=n[r](o);return i}}function yo(t,e){const i=T(T({},t),e),s={};for(const n in i)t[n]!==void 0&&e[n]!==void 0&&(s[n]=si(t[n])(t[n],e[n]));return n=>{for(const o in s)i[o]=s[o](n);return i}}function vo(t,e){var n;const i=[],s={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const r=e.types[o],a=t.indexes[r][s[r]],l=(n=t.values[a])!=null?n:0;i[o]=l,s[r]++}return i}const To=(t,e)=>{const i=st.createTransformer(e),s=Lt(t),n=Lt(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?Pe.has(t)&&!n.values.length||Pe.has(e)&&!s.values.length?mo(t,e):It(mn(vo(s,n),n.values),i):qt(t,e)};function gn(t,e,i){return typeof t=="number"&&typeof e=="number"&&typeof i=="number"?D(t,e,i):si(t)(t,e)}const xo=t=>{const e=({timestamp:i})=>t(i);return{start:(i=!0)=>R.update(e,i),stop:()=>it(e),now:()=>B.isProcessing?B.timestamp:U.now()}},yn=(t,e,i=10)=>{let s="";const n=Math.max(Math.round(e/i),2);for(let o=0;o<n;o++)s+=Math.round(t(o/(n-1))*1e4)/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},Zt=2e4;function ni(t){let e=0;const i=50;let s=t.next(e);for(;!s.done&&e<Zt;)e+=i,s=t.next(e);return e>=Zt?1/0:e}function So(t,e=100,i){const s=i(C(T({},t),{keyframes:[0,e]})),n=Math.min(ni(s),Zt);return{type:"keyframes",ease:o=>s.next(n*o).value/e,duration:Y(n)}}const wo=5;function vn(t,e,i){const s=Math.max(e-wo,0);return qs(i-t(s),e-s)}const L={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},pe=.001;function bo({duration:t=L.duration,bounce:e=L.bounce,velocity:i=L.velocity,mass:s=L.mass}){let n,o,r=1-e;r=J(L.minDamping,L.maxDamping,r),t=J(L.minDuration,L.maxDuration,Y(t)),r<1?(n=u=>{const c=u*r,h=c*t,f=c-i,d=Ae(u,r),p=Math.exp(-h);return pe-f/d*p},o=u=>{const h=u*r*t,f=h*i+i,d=Math.pow(r,2)*Math.pow(u,2)*t,p=Math.exp(-h),m=Ae(Math.pow(u,2),r);return(-n(u)+pe>0?-1:1)*((f-d)*p)/m}):(n=u=>{const c=Math.exp(-u*t),h=(u-i)*t+1;return-pe+c*h},o=u=>{const c=Math.exp(-u*t),h=(i-u)*(t*t);return c*h});const a=5/t,l=Ao(n,o,a);if(t=X(t),isNaN(l))return{stiffness:L.stiffness,damping:L.damping,duration:t};{const u=Math.pow(l,2)*s;return{stiffness:u,damping:r*2*Math.sqrt(s*u),duration:t}}}const Po=12;function Ao(t,e,i){let s=i;for(let n=1;n<Po;n++)s=s-t(s)/e(s);return s}function Ae(t,e){return t*Math.sqrt(1-e*e)}const Vo=["duration","bounce"],Mo=["stiffness","damping","mass"];function ji(t,e){return e.some(i=>t[i]!==void 0)}function Co(t){let e=T({velocity:L.velocity,stiffness:L.stiffness,damping:L.damping,mass:L.mass,isResolvedFromDuration:!1},t);if(!ji(t,Mo)&&ji(t,Vo))if(t.visualDuration){const i=t.visualDuration,s=2*Math.PI/(i*1.2),n=s*s,o=2*J(.05,1,1-(t.bounce||0))*Math.sqrt(n);e=C(T({},e),{mass:L.mass,stiffness:n,damping:o})}else{const i=bo(t);e=C(T(T({},e),i),{mass:L.mass}),e.isResolvedFromDuration=!0}return e}function Jt(t=L.visualDuration,e=L.bounce){const i=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:n}=i;const o=i.keyframes[0],r=i.keyframes[i.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:h,velocity:f,isResolvedFromDuration:d}=Co(C(T({},i),{velocity:-Y(i.velocity||0)})),p=f||0,m=u/(2*Math.sqrt(l*c)),g=r-o,y=Y(Math.sqrt(l/c)),S=Math.abs(g)<5;s||(s=S?L.restSpeed.granular:L.restSpeed.default),n||(n=S?L.restDelta.granular:L.restDelta.default);let v;if(m<1){const x=Ae(y,m);v=A=>{const E=Math.exp(-m*y*A);return r-E*((p+m*y*g)/x*Math.sin(x*A)+g*Math.cos(x*A))}}else if(m===1)v=x=>r-Math.exp(-y*x)*(g+(p+y*g)*x);else{const x=y*Math.sqrt(m*m-1);v=A=>{const E=Math.exp(-m*y*A),M=Math.min(x*A,300);return r-E*((p+m*y*g)*Math.sinh(M)+x*g*Math.cosh(M))/x}}const V={calculatedDuration:d&&h||null,next:x=>{const A=v(x);if(d)a.done=x>=h;else{let E=x===0?p:0;m<1&&(E=x===0?X(p):vn(v,x,A));const M=Math.abs(E)<=s,O=Math.abs(r-A)<=n;a.done=M&&O}return a.value=a.done?r:A,a},toString:()=>{const x=Math.min(ni(V),Zt),A=yn(E=>V.next(x*E).value,x,30);return x+"ms "+A},toTransition:()=>{}};return V}Jt.applyToOptions=t=>{const e=So(t,100,Jt);return t.ease=e.ease,t.duration=X(e.duration),t.type="keyframes",t};function Ve({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],f={done:!1,value:h},d=M=>a!==void 0&&M<a||l!==void 0&&M>l,p=M=>a===void 0?l:l===void 0||Math.abs(a-M)<Math.abs(l-M)?a:l;let m=i*e;const g=h+m,y=r===void 0?g:r(g);y!==g&&(m=y-h);const S=M=>-m*Math.exp(-M/s),v=M=>y+S(M),V=M=>{const O=S(M),W=v(M);f.done=Math.abs(O)<=u,f.value=f.done?y:W};let x,A;const E=M=>{d(f.value)&&(x=M,A=Jt({keyframes:[f.value,p(f.value)],velocity:vn(v,M,f.value),damping:n,stiffness:o,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:M=>{let O=!1;return!A&&x===void 0&&(O=!0,V(M),E(M)),x!==void 0&&M>=x?A.next(M-x):(!O&&V(M),f)}}}function Eo(t,e,i){const s=[],n=i||Q.mix||gn,o=t.length-1;for(let r=0;r<o;r++){let a=n(t[r],t[r+1]);if(e){const l=Array.isArray(e)?e[r]||$:e;a=It(l,a)}s.push(a)}return s}function Do(t,e,{clamp:i=!0,ease:s,mixer:n}={}){const o=t.length;if(Ye(o===e.length),o===1)return()=>e[0];if(o===2&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=Eo(e,s,n),l=a.length,u=c=>{if(r&&c<t[0])return e[0];let h=0;if(l>1)for(;h<t.length-2&&!(c<t[h+1]);h++);const f=Dt(t[h],t[h+1],c);return a[h](f)};return i?c=>u(J(t[0],t[o-1],c)):u}function Ro(t,e){const i=t[t.length-1];for(let s=1;s<=e;s++){const n=Dt(0,e,s);t.push(D(i,1,n))}}function Lo(t){const e=[0];return Ro(e,t.length-1),e}function ko(t,e){return t.map(i=>i*e)}function Fo(t,e){return t.map(()=>e||on).splice(0,t.length-1)}function Vt({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){const n=Hr(s)?s.map(Li):Li(s),o={done:!1,value:e[0]},r=ko(i&&i.length===e.length?i:Lo(e),t),a=Do(r,e,{ease:Array.isArray(n)?n:Fo(e,n)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const Oo=t=>t!==null;function ri(t,{repeat:e,repeatType:i="loop"},s,n=1){const o=t.filter(Oo),a=n<0||e&&i!=="loop"&&e%2===1?0:o.length-1;return!a||s===void 0?o[a]:s}const Bo={decay:Ve,inertia:Ve,tween:Vt,keyframes:Vt,spring:Jt};function Tn(t){typeof t.type=="string"&&(t.type=Bo[t.type])}class oi{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,i){return this.finished.then(e,i)}}const Io=t=>t/100;class ai extends oi{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var s,n;const{motionValue:i}=this.options;i&&i.updatedAt!==U.now()&&this.tick(U.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(n=(s=this.options).onStop)==null||n.call(s))},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;Tn(e);const{type:i=Vt,repeat:s=0,repeatDelay:n=0,repeatType:o,velocity:r=0}=e;let{keyframes:a}=e;const l=i||Vt;l!==Vt&&typeof a[0]!="number"&&(this.mixKeyframes=It(Io,gn(a[0],a[1])),a=[0,100]);const u=l(C(T({},e),{keyframes:a}));o==="mirror"&&(this.mirroredGenerator=l(C(T({},e),{keyframes:[...a].reverse(),velocity:-r}))),u.calculatedDuration===null&&(u.calculatedDuration=ni(u));const{calculatedDuration:c}=u;this.calculatedDuration=c,this.resolvedDuration=c+n,this.totalDuration=this.resolvedDuration*(s+1)-n,this.generator=u}updateTime(e){const i=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=i}tick(e,i=!1){const{generator:s,totalDuration:n,mixKeyframes:o,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:l}=this;if(this.startTime===null)return s.next(0);const{delay:u=0,keyframes:c,repeat:h,repeatType:f,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-n/this.speed,this.startTime)),i?this.currentTime=e:this.updateTime(e);const y=this.currentTime-u*(this.playbackSpeed>=0?1:-1),S=this.playbackSpeed>=0?y<0:y>n;this.currentTime=Math.max(y,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=n);let v=this.currentTime,V=s;if(h){const M=Math.min(this.currentTime,n)/a;let O=Math.floor(M),W=M%1;!W&&M>=1&&(W=1),W===1&&O--,O=Math.min(O,h+1),!!(O%2)&&(f==="reverse"?(W=1-W,d&&(W-=d/a)):f==="mirror"&&(V=r)),v=J(0,1,W)*a}const x=S?{done:!1,value:c[0]}:V.next(v);o&&(x.value=o(x.value));let{done:A}=x;!S&&l!==null&&(A=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);const E=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&A);return E&&p!==Ve&&(x.value=ri(c,this.options,g,this.speed)),m&&m(x.value),E&&this.finish(),x}then(e,i){return this.finished.then(e,i)}get duration(){return Y(this.calculatedDuration)}get time(){return Y(this.currentTime)}set time(e){var i;e=X(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),(i=this.driver)==null||i.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(U.now());const i=this.playbackSpeed!==e;this.playbackSpeed=e,i&&(this.time=Y(this.currentTime))}play(){var n,o;if(this.isStopped)return;const{driver:e=xo,startTime:i}=this.options;this.driver||(this.driver=e(r=>this.tick(r))),(o=(n=this.options).onPlay)==null||o.call(n);const s=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=s):this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime||(this.startTime=i!=null?i:s),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(U.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,i;this.notifyFinished(),this.teardown(),this.state="finished",(i=(e=this.options).onComplete)==null||i.call(e)}cancel(){var e,i;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(i=(e=this.options).onCancel)==null||i.call(e)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var i;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(i=this.driver)==null||i.stop(),e.observe(this)}}function jo(t){var e;for(let i=1;i<t.length;i++)(e=t[i])!=null||(t[i]=t[i-1])}const lt=t=>t*180/Math.PI,Me=t=>{const e=lt(Math.atan2(t[1],t[0]));return Ce(e)},No={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Me,rotateZ:Me,skewX:t=>lt(Math.atan(t[1])),skewY:t=>lt(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ce=t=>(t=t%360,t<0&&(t+=360),t),Ni=Me,Wi=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Ui=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Wo={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Wi,scaleY:Ui,scale:t=>(Wi(t)+Ui(t))/2,rotateX:t=>Ce(lt(Math.atan2(t[6],t[5]))),rotateY:t=>Ce(lt(Math.atan2(-t[2],t[0]))),rotateZ:Ni,rotate:Ni,skewX:t=>lt(Math.atan(t[4])),skewY:t=>lt(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Ee(t){return t.includes("scale")?1:0}function De(t,e){if(!t||t==="none")return Ee(e);const i=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,n;if(i)s=Wo,n=i;else{const a=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=No,n=a}if(!n)return Ee(e);const o=s[e],r=n[1].split(",").map(zo);return typeof o=="function"?o(r):r[o]}const Uo=(t,e)=>{const{transform:i="none"}=getComputedStyle(t);return De(i,e)};function zo(t){return parseFloat(t.trim())}const xt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],St=new Set(xt),zi=t=>t===Tt||t===P,_o=new Set(["x","y","z"]),Ho=xt.filter(t=>!_o.has(t));function $o(t){const e=[];return Ho.forEach(i=>{const s=t.getValue(i);s!==void 0&&(e.push([i,s.get()]),s.set(i.startsWith("scale")?1:0))}),e}const ct={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>De(e,"x"),y:(t,{transform:e})=>De(e,"y")};ct.translateX=ct.x;ct.translateY=ct.y;const ut=new Set;let Re=!1,Le=!1,ke=!1;function xn(){if(Le){const t=Array.from(ut).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),i=new Map;e.forEach(s=>{const n=$o(s);n.length&&(i.set(s,n),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const n=i.get(s);n&&n.forEach(([o,r])=>{var a;(a=s.getValue(o))==null||a.set(r)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}Le=!1,Re=!1,ut.forEach(t=>t.complete(ke)),ut.clear()}function Sn(){ut.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Le=!0)})}function Ko(){ke=!0,Sn(),xn(),ke=!1}class li{constructor(e,i,s,n,o,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=i,this.name=s,this.motionValue=n,this.element=o,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(ut.add(this),Re||(Re=!0,R.read(Sn),R.resolveKeyframes(xn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:i,element:s,motionValue:n}=this;if(e[0]===null){const o=n==null?void 0:n.get(),r=e[e.length-1];if(o!==void 0)e[0]=o;else if(s&&i){const a=s.readValue(i,r);a!=null&&(e[0]=a)}e[0]===void 0&&(e[0]=r),n&&o===void 0&&n.set(e[0])}jo(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),ut.delete(this)}cancel(){this.state==="scheduled"&&(ut.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const Xo=t=>t.startsWith("--");function Yo(t,e,i){Xo(e)?t.style.setProperty(e,i):t.style[e]=i}const Go=Ge(()=>window.ScrollTimeline!==void 0),qo={};function Zo(t,e){const i=Ge(t);return()=>{var s;return(s=qo[e])!=null?s:i()}}const wn=Zo(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),Pt=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,_i={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Pt([0,.65,.55,1]),circOut:Pt([.55,0,1,.45]),backIn:Pt([.31,.01,.66,-.59]),backOut:Pt([.33,1.53,.69,.99])};function bn(t,e){if(t)return typeof t=="function"?wn()?yn(t,e):"ease-out":an(t)?Pt(t):Array.isArray(t)?t.map(i=>bn(i,e)||_i.easeOut):_i[t]}function Jo(t,e,i,{delay:s=0,duration:n=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},u=void 0){const c={[e]:i};l&&(c.offset=l);const h=bn(a,n);Array.isArray(h)&&(c.easing=h);const f={delay:s,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"};return u&&(f.pseudoElement=u),t.animate(c,f)}function Pn(t){return typeof t=="function"&&"applyToOptions"in t}function Qo(i){var s=i,{type:t}=s,e=z(s,["type"]);var n,o;return Pn(t)&&wn()?t.applyToOptions(e):((n=e.duration)!=null||(e.duration=300),(o=e.ease)!=null||(e.ease="easeOut"),e)}class ta extends oi{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:i,name:s,keyframes:n,pseudoElement:o,allowFlatten:r=!1,finalKeyframe:a,onComplete:l}=e;this.isPseudoElement=!!o,this.allowFlatten=r,this.options=e,Ye(typeof e.type!="string");const u=Qo(e);this.animation=Jo(i,s,n,u,o),u.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!o){const c=ri(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(c):Yo(i,s,c),this.animation.cancel()}l==null||l(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,i;(i=(e=this.animation).finish)==null||i.call(e)}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var e,i;this.isPseudoElement||(i=(e=this.animation).commitStyles)==null||i.call(e)}get duration(){var i,s;const e=((s=(i=this.animation.effect)==null?void 0:i.getComputedTiming)==null?void 0:s.call(i).duration)||0;return Y(Number(e))}get time(){return Y(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=X(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:i}){var s;return this.allowFlatten&&((s=this.animation.effect)==null||s.updateTiming({easing:"linear"})),this.animation.onfinish=null,e&&Go()?(this.animation.timeline=e,$):i(this)}}const An={anticipate:sn,backInOut:en,circInOut:rn};function ea(t){return t in An}function ia(t){typeof t.ease=="string"&&ea(t.ease)&&(t.ease=An[t.ease])}const Hi=10;class sa extends ta{constructor(e){ia(e),Tn(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){var c;const u=this.options,{motionValue:i,onUpdate:s,onComplete:n,element:o}=u,r=z(u,["motionValue","onUpdate","onComplete","element"]);if(!i)return;if(e!==void 0){i.set(e);return}const a=new ai(C(T({},r),{autoplay:!1})),l=X((c=this.finishedTime)!=null?c:this.time);i.setWithVelocity(a.sample(l-Hi).value,a.sample(l).value,Hi),a.stop()}}const $i=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(st.test(t)||t==="0")&&!t.startsWith("url("));function na(t){const e=t[0];if(t.length===1)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}function ra(t,e,i,s){const n=t[0];if(n===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],r=$i(n,e),a=$i(o,e);return!r||!a?!1:na(t)||(i==="spring"||Pn(i))&&s}function ci(t){return Ys(t)&&"offsetHeight"in t}const oa=new Set(["opacity","clipPath","filter","transform"]),aa=Ge(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function la(t){var u;const{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:o,type:r}=t;if(!ci((u=e==null?void 0:e.owner)==null?void 0:u.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return aa()&&i&&oa.has(i)&&(i!=="transform"||!l)&&!a&&!s&&n!=="mirror"&&o!==0&&r!=="inertia"}const ca=40;class ua extends oi{constructor(f){var d=f,{autoplay:e=!0,delay:i=0,type:s="keyframes",repeat:n=0,repeatDelay:o=0,repeatType:r="loop",keyframes:a,name:l,motionValue:u,element:c}=d,h=z(d,["autoplay","delay","type","repeat","repeatDelay","repeatType","keyframes","name","motionValue","element"]);var g;super(),this.stop=()=>{var y,S;this._animation&&(this._animation.stop(),(y=this.stopTimeline)==null||y.call(this)),(S=this.keyframeResolver)==null||S.cancel()},this.createdAt=U.now();const p=T({autoplay:e,delay:i,type:s,repeat:n,repeatDelay:o,repeatType:r,name:l,motionValue:u,element:c},h),m=(c==null?void 0:c.KeyframeResolver)||li;this.keyframeResolver=new m(a,(y,S,v)=>this.onKeyframesResolved(y,S,p,!v),l,u,c),(g=this.keyframeResolver)==null||g.scheduleResolve()}onKeyframesResolved(e,i,s,n){this.keyframeResolver=void 0;const{name:o,type:r,velocity:a,delay:l,isHandoff:u,onUpdate:c}=s;this.resolvedAt=U.now(),ra(e,o,r,a)||((Q.instantAnimations||!l)&&(c==null||c(ri(e,s,i))),e[0]=e[e.length-1],s.duration=0,s.repeat=0);const h=n?this.resolvedAt?this.resolvedAt-this.createdAt>ca?this.resolvedAt:this.createdAt:this.createdAt:void 0,f=C(T({startTime:h,finalKeyframe:i},s),{keyframes:e}),d=!u&&la(f)?new sa(C(T({},f),{element:f.motionValue.owner.current})):new ai(f);d.finished.then(()=>this.notifyFinished()).catch($),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,i){return this.finished.finally(e).then(()=>{})}get animation(){var e;return this._animation||((e=this.keyframeResolver)==null||e.resume(),Ko()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),(e=this.keyframeResolver)==null||e.cancel()}}const ha=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function fa(t){const e=ha.exec(t);if(!e)return[,];const[,i,s,n]=e;return[`--${i!=null?i:s}`,n]}function Vn(t,e,i=1){const[s,n]=fa(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const r=o.trim();return Xs(r)?parseFloat(r):r}return ti(n)?Vn(n,e,i+1):n}function ui(t,e){var i,s;return(s=(i=t==null?void 0:t[e])!=null?i:t==null?void 0:t.default)!=null?s:t}const Mn=new Set(["width","height","top","left","right","bottom",...xt]),da={test:t=>t==="auto",parse:t=>t},Cn=t=>e=>e.test(t),En=[Tt,P,G,et,so,io,da],Ki=t=>En.find(Cn(t));function pa(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Gs(t):!0}const ma=new Set(["brightness","contrast","saturate","opacity"]);function ga(t){const[e,i]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=i.match(ei)||[];if(!s)return t;const n=i.replace(s,"");let o=ma.has(e)?1:0;return s!==i&&(o*=100),e+"("+o+n+")"}const ya=/\b([a-z-]*)\(.*?\)/gu,Fe=C(T({},st),{getAnimatableNone:t=>{const e=t.match(ya);return e?e.map(ga).join(" "):t}}),Xi=C(T({},Tt),{transform:Math.round}),va={rotate:et,rotateX:et,rotateY:et,rotateZ:et,scale:_t,scaleX:_t,scaleY:_t,scaleZ:_t,skew:et,skewX:et,skewY:et,distance:P,translateX:P,translateY:P,translateZ:P,x:P,y:P,z:P,perspective:P,transformPerspective:P,opacity:Rt,originX:Fi,originY:Fi,originZ:P},hi=C(T({borderWidth:P,borderTopWidth:P,borderRightWidth:P,borderBottomWidth:P,borderLeftWidth:P,borderRadius:P,radius:P,borderTopLeftRadius:P,borderTopRightRadius:P,borderBottomRightRadius:P,borderBottomLeftRadius:P,width:P,maxWidth:P,height:P,maxHeight:P,top:P,right:P,bottom:P,left:P,padding:P,paddingTop:P,paddingRight:P,paddingBottom:P,paddingLeft:P,margin:P,marginTop:P,marginRight:P,marginBottom:P,marginLeft:P,backgroundPositionX:P,backgroundPositionY:P},va),{zIndex:Xi,fillOpacity:Rt,strokeOpacity:Rt,numOctaves:Xi}),Ta=C(T({},hi),{color:F,backgroundColor:F,outlineColor:F,fill:F,stroke:F,borderColor:F,borderTopColor:F,borderRightColor:F,borderBottomColor:F,borderLeftColor:F,filter:Fe,WebkitFilter:Fe}),Dn=t=>Ta[t];function Rn(t,e){let i=Dn(t);return i!==Fe&&(i=st),i.getAnimatableNone?i.getAnimatableNone(e):void 0}const xa=new Set(["auto","none","0"]);function Sa(t,e,i){let s=0,n;for(;s<t.length&&!n;){const o=t[s];typeof o=="string"&&!xa.has(o)&&Lt(o).values.length&&(n=t[s]),s++}if(n&&i)for(const o of e)t[o]=Rn(i,n)}class wa extends li{constructor(e,i,s,n,o){super(e,i,s,n,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:i,name:s}=this;if(!i||!i.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let u=e[l];if(typeof u=="string"&&(u=u.trim(),ti(u))){const c=Vn(u,i.current);c!==void 0&&(e[l]=c),l===e.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!Mn.has(s)||e.length!==2)return;const[n,o]=e,r=Ki(n),a=Ki(o);if(r!==a)if(zi(r)&&zi(a))for(let l=0;l<e.length;l++){const u=e[l];typeof u=="string"&&(e[l]=parseFloat(u))}else ct[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:i}=this,s=[];for(let n=0;n<e.length;n++)(e[n]===null||pa(e[n]))&&s.push(n);s.length&&Sa(e,s,i)}measureInitialState(){const{element:e,unresolvedKeyframes:i,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ct[s](e.measureViewportBox(),window.getComputedStyle(e.current)),i[0]=this.measuredOrigin;const n=i[i.length-1];n!==void 0&&e.getValue(s,n).jump(n,!1)}measureEndState(){var a;const{element:e,name:i,unresolvedKeyframes:s}=this;if(!e||!e.current)return;const n=e.getValue(i);n&&n.jump(this.measuredOrigin,!1);const o=s.length-1,r=s[o];s[o]=ct[i](e.measureViewportBox(),window.getComputedStyle(e.current)),r!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=r),(a=this.removedTransforms)!=null&&a.length&&this.removedTransforms.forEach(([l,u])=>{e.getValue(l).set(u)}),this.resolveNoneKeyframes()}}function ba(t,e,i){var s;if(t instanceof EventTarget)return[t];if(typeof t=="string"){let n=document;const o=(s=i==null?void 0:i[t])!=null?s:n.querySelectorAll(t);return o?Array.from(o):[]}return Array.from(t)}const Ln=(t,e)=>e&&typeof t=="number"?e.transform(t):t,Yi=30,Pa=t=>!isNaN(parseFloat(t));class Aa{constructor(e,i={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,n=!0)=>{var r,a;const o=U.now();if(this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&((r=this.events.change)==null||r.notify(this.current),this.dependents))for(const l of this.dependents)l.dirty();n&&((a=this.events.renderRequest)==null||a.notify(this.current))},this.hasAnimated=!1,this.setCurrent(e),this.owner=i.owner}setCurrent(e){this.current=e,this.updatedAt=U.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=Pa(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,i){this.events[e]||(this.events[e]=new qe);const s=this.events[e].add(i);return e==="change"?()=>{s(),R.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,i){this.passiveEffect=e,this.stopPassiveEffect=i}set(e,i=!0){!i||!this.passiveEffect?this.updateAndNotify(e,i):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,i,s){this.set(i),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,i=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,i&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;(e=this.events.change)==null||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=U.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Yi)return 0;const i=Math.min(this.updatedAt-this.prevUpdatedAt,Yi);return qs(parseFloat(this.current)-parseFloat(this.prevFrameValue),i)}start(e){return this.stop(),new Promise(i=>{this.hasAnimated=!0,this.animation=e(i),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,i;(e=this.dependents)==null||e.clear(),(i=this.events.destroy)==null||i.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function yt(t,e){return new Aa(t,e)}const{schedule:fi}=ln(queueMicrotask,!1),K={x:!1,y:!1};function kn(){return K.x||K.y}function Va(t){return t==="x"||t==="y"?K[t]?null:(K[t]=!0,()=>{K[t]=!1}):K.x||K.y?null:(K.x=K.y=!0,()=>{K.x=K.y=!1})}function Fn(t,e){const i=ba(t),s=new AbortController,n=C(T({passive:!0},e),{signal:s.signal});return[i,n,()=>s.abort()]}function Gi(t){return!(t.pointerType==="touch"||kn())}function Ma(t,e,i={}){const[s,n,o]=Fn(t,i),r=a=>{if(!Gi(a))return;const{target:l}=a,u=e(l,a);if(typeof u!="function"||!l)return;const c=h=>{Gi(h)&&(u(h),l.removeEventListener("pointerleave",c))};l.addEventListener("pointerleave",c,n)};return s.forEach(a=>{a.addEventListener("pointerenter",r,n)}),o}const On=(t,e)=>e?t===e?!0:On(t,e.parentElement):!1,di=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,Ca=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Ea(t){return Ca.has(t.tagName)||t.tabIndex!==-1}const Xt=new WeakSet;function qi(t){return e=>{e.key==="Enter"&&t(e)}}function me(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const Da=(t,e)=>{const i=t.currentTarget;if(!i)return;const s=qi(()=>{if(Xt.has(i))return;me(i,"down");const n=qi(()=>{me(i,"up")}),o=()=>me(i,"cancel");i.addEventListener("keyup",n,e),i.addEventListener("blur",o,e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function Zi(t){return di(t)&&!kn()}function Ra(t,e,i={}){const[s,n,o]=Fn(t,i),r=a=>{const l=a.currentTarget;if(!Zi(a))return;Xt.add(l);const u=e(l,a),c=(d,p)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),Xt.has(l)&&Xt.delete(l),Zi(d)&&typeof u=="function"&&u(d,{success:p})},h=d=>{c(d,l===window||l===document||i.useGlobalTarget||On(l,d.target))},f=d=>{c(d,!1)};window.addEventListener("pointerup",h,n),window.addEventListener("pointercancel",f,n)};return s.forEach(a=>{(i.useGlobalTarget?window:a).addEventListener("pointerdown",r,n),ci(a)&&(a.addEventListener("focus",u=>Da(u,n)),!Ea(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),o}function Bn(t){return Ys(t)&&"ownerSVGElement"in t}function La(t){return Bn(t)&&t.tagName==="svg"}const I=t=>!!(t&&t.getVelocity),ka=[...En,F,st],Fa=t=>ka.find(Cn(t)),pi=w.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class Oa extends w.Component{getSnapshotBeforeUpdate(e){const i=this.props.childRef.current;if(i&&e.isPresent&&!this.props.isPresent){const s=i.offsetParent,n=ci(s)&&s.offsetWidth||0,o=this.props.sizeRef.current;o.height=i.offsetHeight||0,o.width=i.offsetWidth||0,o.top=i.offsetTop,o.left=i.offsetLeft,o.right=n-o.width-o.left}return null}componentDidUpdate(){}render(){return this.props.children}}function Ba({children:t,isPresent:e,anchorX:i}){const s=w.useId(),n=w.useRef(null),o=w.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:r}=w.useContext(pi);return w.useInsertionEffect(()=>{const{width:a,height:l,top:u,left:c,right:h}=o.current;if(e||!n.current||!a||!l)return;const f=i==="left"?`left: ${c}`:`right: ${h}`;n.current.dataset.motionPopId=s;const d=document.createElement("style");return r&&(d.nonce=r),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${l}px !important;
            ${f}px !important;
            top: ${u}px !important;
          }
        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[e]),Z.jsx(Oa,{isPresent:e,childRef:n,sizeRef:o,children:w.cloneElement(t,{ref:n})})}const Ia=({children:t,initial:e,isPresent:i,onExitComplete:s,custom:n,presenceAffectsLayout:o,mode:r,anchorX:a})=>{const l=He(ja),u=w.useId();let c=!0,h=w.useMemo(()=>(c=!1,{id:u,initial:e,isPresent:i,custom:n,onExitComplete:f=>{l.set(f,!0);for(const d of l.values())if(!d)return;s&&s()},register:f=>(l.set(f,!1),()=>l.delete(f))}),[i,l,s]);return o&&c&&(h=T({},h)),w.useMemo(()=>{l.forEach((f,d)=>l.set(d,!1))},[i]),w.useEffect(()=>{!i&&!l.size&&s&&s()},[i]),r==="popLayout"&&(t=Z.jsx(Ba,{isPresent:i,anchorX:a,children:t})),Z.jsx(ee.Provider,{value:h,children:t})};function ja(){return new Map}function In(t=!0){const e=w.useContext(ee);if(e===null)return[!0,null];const{isPresent:i,onExitComplete:s,register:n}=e,o=w.useId();w.useEffect(()=>{if(t)return n(o)},[t]);const r=w.useCallback(()=>t&&s&&s(o),[o,s,t]);return!i&&s?[!1,r]:[!0]}const Ht=t=>t.key||"";function Ji(t){const e=[];return w.Children.forEach(t,i=>{w.isValidElement(i)&&e.push(i)}),e}const Mu=({children:t,custom:e,initial:i=!0,onExitComplete:s,presenceAffectsLayout:n=!0,mode:o="sync",propagate:r=!1,anchorX:a="left"})=>{const[l,u]=In(r),c=w.useMemo(()=>Ji(t),[t]),h=r&&!l?[]:c.map(Ht),f=w.useRef(!0),d=w.useRef(c),p=He(()=>new Map),[m,g]=w.useState(c),[y,S]=w.useState(c);Ks(()=>{f.current=!1,d.current=c;for(let x=0;x<y.length;x++){const A=Ht(y[x]);h.includes(A)?p.delete(A):p.get(A)!==!0&&p.set(A,!1)}},[y,h.length,h.join("-")]);const v=[];if(c!==m){let x=[...c];for(let A=0;A<y.length;A++){const E=y[A],M=Ht(E);h.includes(M)||(x.splice(A,0,E),v.push(E))}return o==="wait"&&v.length&&(x=v),S(Ji(x)),g(c),null}const{forceRender:V}=w.useContext(_e);return Z.jsx(Z.Fragment,{children:y.map(x=>{const A=Ht(x),E=r&&!l?!1:c===y||h.includes(A),M=()=>{if(p.has(A))p.set(A,!0);else return;let O=!0;p.forEach(W=>{W||(O=!1)}),O&&(V==null||V(),S(d.current),r&&(u==null||u()),s&&s())};return Z.jsx(Ia,{isPresent:E,initial:!f.current||i?void 0:!1,custom:e,presenceAffectsLayout:n,mode:o,onExitComplete:E?void 0:M,anchorX:a,children:x},A)})})},jn=w.createContext({strict:!1}),Qi={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},vt={};for(const t in Qi)vt[t]={isEnabled:e=>Qi[t].some(i=>!!e[i])};function Na(t){for(const e in t)vt[e]=T(T({},vt[e]),t[e])}const Wa=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Qt(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Wa.has(t)}let Nn=t=>!Qt(t);function Ua(t){typeof t=="function"&&(Nn=e=>e.startsWith("on")?!Qt(e):t(e))}try{Ua(require("@emotion/is-prop-valid").default)}catch(t){}function za(t,e,i){const s={};for(const n in t)n==="values"&&typeof t.values=="object"||(Nn(n)||i===!0&&Qt(n)||!e&&!Qt(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}function _a(t){if(typeof Proxy=="undefined")return t;const e=new Map,i=(...s)=>t(...s);return new Proxy(i,{get:(s,n)=>n==="create"?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}const ie=w.createContext({});function se(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function kt(t){return typeof t=="string"||Array.isArray(t)}const mi=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],gi=["initial",...mi];function ne(t){return se(t.animate)||gi.some(e=>kt(t[e]))}function Wn(t){return!!(ne(t)||t.variants)}function Ha(t,e){if(ne(t)){const{initial:i,animate:s}=t;return{initial:i===!1||kt(i)?i:void 0,animate:kt(s)?s:void 0}}return t.inherit!==!1?e:{}}function $a(t){const{initial:e,animate:i}=Ha(t,w.useContext(ie));return w.useMemo(()=>({initial:e,animate:i}),[ts(e),ts(i)])}function ts(t){return Array.isArray(t)?t.join(" "):t}const Ka=Symbol.for("motionComponentSymbol");function dt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function Xa(t,e,i){return w.useCallback(s=>{s&&t.onMount&&t.onMount(s),e&&(s?e.mount(s):e.unmount()),i&&(typeof i=="function"?i(s):dt(i)&&(i.current=s))},[e])}const yi=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Ya="framerAppearId",Un="data-"+yi(Ya),zn=w.createContext({});function Ga(t,e,i,s,n){var m,g;const{visualElement:o}=w.useContext(ie),r=w.useContext(jn),a=w.useContext(ee),l=w.useContext(pi).reducedMotion,u=w.useRef(null);s=s||r.renderer,!u.current&&s&&(u.current=s(t,{visualState:e,parent:o,props:i,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));const c=u.current,h=w.useContext(zn);c&&!c.projection&&n&&(c.type==="html"||c.type==="svg")&&qa(u.current,i,n,h);const f=w.useRef(!1);w.useInsertionEffect(()=>{c&&f.current&&c.update(i,a)});const d=i[Un],p=w.useRef(!!d&&!((m=window.MotionHandoffIsComplete)!=null&&m.call(window,d))&&((g=window.MotionHasOptimisedAnimation)==null?void 0:g.call(window,d)));return Ks(()=>{c&&(f.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),fi.render(c.render),p.current&&c.animationState&&c.animationState.animateChanges())}),w.useEffect(()=>{c&&(!p.current&&c.animationState&&c.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{var y;(y=window.MotionHandoffMarkAsComplete)==null||y.call(window,d)}),p.current=!1))}),c}function qa(t,e,i,s){const{layoutId:n,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:_n(t.parent)),t.projection.setOptions({layoutId:n,layout:o,alwaysMeasureLayout:!!r||a&&dt(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:s,crossfade:c,layoutScroll:l,layoutRoot:u})}function _n(t){if(t)return t.options.allowProjection!==!1?t.projection:_n(t.parent)}function Za({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:s,Component:n}){var a,l;t&&Na(t);function o(u,c){let h;const f=C(T(T({},w.useContext(pi)),u),{layoutId:Ja(u)}),{isStatic:d}=f,p=$a(u),m=s(u,d);if(!d&&$e){Qa();const g=tl(f);h=g.MeasureLayout,p.visualElement=Ga(n,m,f,e,g.ProjectionNode)}return Z.jsxs(ie.Provider,{value:p,children:[h&&p.visualElement?Z.jsx(h,T({visualElement:p.visualElement},f)):null,i(n,u,Xa(m,p.visualElement,c),m,d,p.visualElement)]})}o.displayName=`motion.${typeof n=="string"?n:`create(${(l=(a=n.displayName)!=null?a:n.name)!=null?l:""})`}`;const r=w.forwardRef(o);return r[Ka]=n,r}function Ja({layoutId:t}){const e=w.useContext(_e).id;return e&&t!==void 0?e+"-"+t:t}function Qa(t,e){w.useContext(jn).strict}function tl(t){const{drag:e,layout:i}=vt;if(!e&&!i)return{};const s=T(T({},e),i);return{MeasureLayout:e!=null&&e.isEnabled(t)||i!=null&&i.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}const Ft={};function el(t){for(const e in t)Ft[e]=t[e],Qe(e)&&(Ft[e].isCSSVariable=!0)}function Hn(t,{layout:e,layoutId:i}){return St.has(t)||t.startsWith("origin")||(e||i!==void 0)&&(!!Ft[t]||t==="opacity")}const il={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},sl=xt.length;function nl(t,e,i){let s="",n=!0;for(let o=0;o<sl;o++){const r=xt[o],a=t[r];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(r.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||i){const u=Ln(a,hi[r]);if(!l){n=!1;const c=il[r]||r;s+=`${c}(${u}) `}i&&(e[r]=u)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}function vi(t,e,i){const{style:s,vars:n,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const u=e[l];if(St.has(l)){r=!0;continue}else if(Qe(l)){n[l]=u;continue}else{const c=Ln(u,hi[l]);l.startsWith("origin")?(a=!0,o[l]=c):s[l]=c}}if(e.transform||(r||i?s.transform=nl(e,t.transform,i):s.transform&&(s.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:c=0}=o;s.transformOrigin=`${l} ${u} ${c}`}}const Ti=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function $n(t,e,i){for(const s in e)!I(e[s])&&!Hn(s,i)&&(t[s]=e[s])}function rl({transformTemplate:t},e){return w.useMemo(()=>{const i=Ti();return vi(i,e,t),Object.assign({},i.vars,i.style)},[e])}function ol(t,e){const i=t.style||{},s={};return $n(s,i,t),Object.assign(s,rl(t,e)),s}function al(t,e){const i={},s=ol(t,e);return t.drag&&t.dragListener!==!1&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i}const ll={offset:"stroke-dashoffset",array:"stroke-dasharray"},cl={offset:"strokeDashoffset",array:"strokeDasharray"};function ul(t,e,i=1,s=0,n=!0){t.pathLength=1;const o=n?ll:cl;t[o.offset]=P.transform(-s);const r=P.transform(e),a=P.transform(i);t[o.array]=`${r} ${a}`}function Kn(t,h,l,u,c){var f=h,{attrX:e,attrY:i,attrScale:s,pathLength:n,pathSpacing:o=1,pathOffset:r=0}=f,a=z(f,["attrX","attrY","attrScale","pathLength","pathSpacing","pathOffset"]);var m,g;if(vi(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:d,style:p}=t;d.transform&&(p.transform=d.transform,delete d.transform),(p.transform||d.transformOrigin)&&(p.transformOrigin=(m=d.transformOrigin)!=null?m:"50% 50%",delete d.transformOrigin),p.transform&&(p.transformBox=(g=c==null?void 0:c.transformBox)!=null?g:"fill-box",delete d.transformBox),e!==void 0&&(d.x=e),i!==void 0&&(d.y=i),s!==void 0&&(d.scale=s),n!==void 0&&ul(d,n,o,r,!1)}const Xn=()=>C(T({},Ti()),{attrs:{}}),Yn=t=>typeof t=="string"&&t.toLowerCase()==="svg";function hl(t,e,i,s){const n=w.useMemo(()=>{const o=Xn();return Kn(o,e,Yn(s),t.transformTemplate,t.style),C(T({},o.attrs),{style:T({},o.style)})},[e]);if(t.style){const o={};$n(o,t.style,t),n.style=T(T({},o),n.style)}return n}const fl=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function xi(t){return typeof t!="string"||t.includes("-")?!1:!!(fl.indexOf(t)>-1||/[A-Z]/u.test(t))}function dl(t=!1){return(i,s,n,{latestValues:o},r)=>{const l=(xi(i)?hl:al)(s,o,r,i),u=za(s,typeof i=="string",t),c=i!==w.Fragment?C(T(T({},u),l),{ref:n}):{},{children:h}=s,f=w.useMemo(()=>I(h)?h.get():h,[h]);return w.createElement(i,C(T({},c),{children:f}))}}function es(t){const e=[{},{}];return t==null||t.values.forEach((i,s)=>{e[0][s]=i.get(),e[1][s]=i.getVelocity()}),e}function Si(t,e,i,s){if(typeof e=="function"){const[n,o]=es(s);e=e(i!==void 0?i:t.custom,n,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[n,o]=es(s);e=e(i!==void 0?i:t.custom,n,o)}return e}function Yt(t){return I(t)?t.get():t}function pl({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,n){return{latestValues:ml(i,s,n,t),renderState:e()}}const Gn=t=>(e,i)=>{const s=w.useContext(ie),n=w.useContext(ee),o=()=>pl(t,e,s,n);return i?o():He(o)};function ml(t,e,i,s){const n={},o=s(t,{});for(const d in o)n[d]=Yt(o[d]);let{initial:r,animate:a}=t;const l=ne(t),u=Wn(t);e&&u&&!l&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let c=i?i.initial===!1:!1;c=c||r===!1;const h=c?a:r;if(h&&typeof h!="boolean"&&!se(h)){const d=Array.isArray(h)?h:[h];for(let p=0;p<d.length;p++){const m=Si(t,d[p]);if(m){const f=m,{transitionEnd:g,transition:y}=f,S=z(f,["transitionEnd","transition"]);for(const v in S){let V=S[v];if(Array.isArray(V)){const x=c?V.length-1:0;V=V[x]}V!==null&&(n[v]=V)}for(const v in g)n[v]=g[v]}}}return n}function wi(t,e,i){var o;const{style:s}=t,n={};for(const r in s)(I(s[r])||e.style&&I(e.style[r])||Hn(r,t)||((o=i==null?void 0:i.getValue(r))==null?void 0:o.liveStyle)!==void 0)&&(n[r]=s[r]);return n}const gl={useVisualState:Gn({scrapeMotionValuesFromProps:wi,createRenderState:Ti})};function qn(t,e,i){const s=wi(t,e,i);for(const n in t)if(I(t[n])||I(e[n])){const o=xt.indexOf(n)!==-1?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n;s[o]=t[n]}return s}const yl={useVisualState:Gn({scrapeMotionValuesFromProps:qn,createRenderState:Xn})};function vl(t,e){return function(s,{forwardMotionProps:n}={forwardMotionProps:!1}){const o=xi(s)?yl:gl,r=C(T({},o),{preloadedFeatures:t,useRender:dl(n),createVisualElement:e,Component:s});return Za(r)}}function Ot(t,e,i){const s=t.getProps();return Si(s,e,i!==void 0?i:s.custom,t)}const Oe=t=>Array.isArray(t);function Tl(t,e,i){t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,yt(i))}function xl(t){return Oe(t)?t[t.length-1]||0:t}function Sl(t,e){let r=Ot(t,e)||{},{transitionEnd:s={},transition:n={}}=r,o=z(r,["transitionEnd","transition"]);o=T(T({},o),s);for(const a in o){const l=xl(o[a]);Tl(t,a,l)}}function wl(t){return!!(I(t)&&t.add)}function Be(t,e){const i=t.getValue("willChange");if(wl(i))return i.add(e);if(!i&&Q.WillChange){const s=new Q.WillChange("auto");t.addValue("willChange",s),s.add(e)}}function Zn(t){return t.props[Un]}const bl=t=>t!==null;function Pl(t,{repeat:e,repeatType:i="loop"},s){const n=t.filter(bl),o=e&&i!=="loop"&&e%2===1?0:n.length-1;return n[o]}const Al={type:"spring",stiffness:500,damping:25,restSpeed:10},Vl=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Ml={type:"keyframes",duration:.8},Cl={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},El=(t,{keyframes:e})=>e.length>2?Ml:St.has(t)?t.startsWith("scale")?Vl(e[1]):Al:Cl;function Dl(h){var f=h,{when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u}=f,c=z(f,["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"]);return!!Object.keys(c).length}const bi=(t,e,i,s={},n,o)=>r=>{const a=ui(s,t)||{},l=a.delay||s.delay||0;let{elapsed:u=0}=s;u=u-X(l);const c=C(T({keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity()},a),{delay:-u,onUpdate:f=>{e.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:n});Dl(a)||Object.assign(c,El(t,c)),c.duration&&(c.duration=X(c.duration)),c.repeatDelay&&(c.repeatDelay=X(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let h=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(h=!0)),(Q.instantAnimations||Q.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,h&&!o&&e.get()!==void 0){const f=Pl(c.keyframes,a);if(f!==void 0){R.update(()=>{c.onUpdate(f),c.onComplete()});return}}return a.isSync?new ai(c):new ua(c)};function Rl({protectedKeys:t,needsAnimating:e},i){const s=t.hasOwnProperty(i)&&e[i]!==!0;return e[i]=!1,s}function Jn(t,e,{delay:i=0,transitionOverride:s,type:n}={}){var h;let c=e,{transition:o=t.getDefaultTransition(),transitionEnd:r}=c,a=z(c,["transition","transitionEnd"]);s&&(o=s);const l=[],u=n&&t.animationState&&t.animationState.getState()[n];for(const f in a){const d=t.getValue(f,(h=t.latestValues[f])!=null?h:null),p=a[f];if(p===void 0||u&&Rl(u,f))continue;const m=T({delay:i},ui(o||{},f)),g=d.get();if(g!==void 0&&!d.isAnimating&&!Array.isArray(p)&&p===g&&!m.velocity)continue;let y=!1;if(window.MotionHandoffAnimation){const v=Zn(t);if(v){const V=window.MotionHandoffAnimation(v,f,R);V!==null&&(m.startTime=V,y=!0)}}Be(t,f),d.start(bi(f,d,p,t.shouldReduceMotion&&Mn.has(f)?{type:!1}:m,t,y));const S=d.animation;S&&l.push(S)}return r&&Promise.all(l).then(()=>{R.update(()=>{r&&Sl(t,r)})}),l}function Ie(t,e,i={}){var l;const s=Ot(t,e,i.type==="exit"?(l=t.presenceContext)==null?void 0:l.custom:void 0);let{transition:n=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(n=i.transitionOverride);const o=s?()=>Promise.all(Jn(t,s,i)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:h,staggerDirection:f}=n;return Ll(t,e,c+u,h,f,i)}:()=>Promise.resolve(),{when:a}=n;if(a){const[u,c]=a==="beforeChildren"?[o,r]:[r,o];return u().then(()=>c())}else return Promise.all([o(),r(i.delay)])}function Ll(t,e,i=0,s=0,n=1,o){const r=[],a=(t.variantChildren.size-1)*s,l=n===1?(u=0)=>u*s:(u=0)=>a-u*s;return Array.from(t.variantChildren).sort(kl).forEach((u,c)=>{u.notify("AnimationStart",e),r.push(Ie(u,e,C(T({},o),{delay:i+l(c)})).then(()=>u.notify("AnimationComplete",e)))}),Promise.all(r)}function kl(t,e){return t.sortNodePosition(e)}function Fl(t,e,i={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const n=e.map(o=>Ie(t,o,i));s=Promise.all(n)}else if(typeof e=="string")s=Ie(t,e,i);else{const n=typeof e=="function"?Ot(t,e,i.custom):e;s=Promise.all(Jn(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})}function Qn(t,e){if(!Array.isArray(e))return!1;const i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}const Ol=gi.length;function tr(t){if(!t)return;if(!t.isControllingVariants){const i=t.parent?tr(t.parent)||{}:{};return t.props.initial!==void 0&&(i.initial=t.props.initial),i}const e={};for(let i=0;i<Ol;i++){const s=gi[i],n=t.props[s];(kt(n)||n===!1)&&(e[s]=n)}return e}const Bl=[...mi].reverse(),Il=mi.length;function jl(t){return e=>Promise.all(e.map(({animation:i,options:s})=>Fl(t,i,s)))}function Nl(t){let e=jl(t),i=is(),s=!0;const n=l=>(u,c)=>{var f;const h=Ot(t,c,l==="exit"?(f=t.presenceContext)==null?void 0:f.custom:void 0);if(h){const d=h,{transition:p,transitionEnd:m}=d,g=z(d,["transition","transitionEnd"]);u=T(T(T({},u),g),m)}return u};function o(l){e=l(t)}function r(l){const{props:u}=t,c=tr(t.parent)||{},h=[],f=new Set;let d={},p=1/0;for(let g=0;g<Il;g++){const y=Bl[g],S=i[y],v=u[y]!==void 0?u[y]:c[y],V=kt(v),x=y===l?S.isActive:null;x===!1&&(p=g);let A=v===c[y]&&v!==u[y]&&V;if(A&&s&&t.manuallyAnimateOnMount&&(A=!1),S.protectedKeys=T({},d),!S.isActive&&x===null||!v&&!S.prevProp||se(v)||typeof v=="boolean")continue;const E=Wl(S.prevProp,v);let M=E||y===l&&S.isActive&&!A&&V||g>p&&V,O=!1;const W=Array.isArray(v)?v:[v];let ht=W.reduce(n(y),{});x===!1&&(ht={});const{prevResolvedValues:Pi={}}=S,br=T(T({},Pi),ht),Ai=j=>{M=!0,f.has(j)&&(O=!0,f.delete(j)),S.needsAnimating[j]=!0;const q=t.getValue(j);q&&(q.liveStyle=!1)};for(const j in br){const q=ht[j],re=Pi[j];if(d.hasOwnProperty(j))continue;let oe=!1;Oe(q)&&Oe(re)?oe=!Qn(q,re):oe=q!==re,oe?q!=null?Ai(j):f.add(j):q!==void 0&&f.has(j)?Ai(j):S.protectedKeys[j]=!0}S.prevProp=v,S.prevResolvedValues=ht,S.isActive&&(d=T(T({},d),ht)),s&&t.blockInitialAnimation&&(M=!1),M&&(!(A&&E)||O)&&h.push(...W.map(j=>({animation:j,options:{type:y}})))}if(f.size){const g={};if(typeof u.initial!="boolean"){const y=Ot(t,Array.isArray(u.initial)?u.initial[0]:u.initial);y&&y.transition&&(g.transition=y.transition)}f.forEach(y=>{const S=t.getBaseTarget(y),v=t.getValue(y);v&&(v.liveStyle=!0),g[y]=S!=null?S:null}),h.push({animation:g})}let m=!!h.length;return s&&(u.initial===!1||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(m=!1),s=!1,m?e(h):Promise.resolve()}function a(l,u){var h;if(i[l].isActive===u)return Promise.resolve();(h=t.variantChildren)==null||h.forEach(f=>{var d;return(d=f.animationState)==null?void 0:d.setActive(l,u)}),i[l].isActive=u;const c=r(l);for(const f in i)i[f].protectedKeys={};return c}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>i,reset:()=>{i=is(),s=!0}}}function Wl(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Qn(e,t):!1}function rt(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function is(){return{animate:rt(!0),whileInView:rt(),whileHover:rt(),whileTap:rt(),whileDrag:rt(),whileFocus:rt(),exit:rt()}}class nt{constructor(e){this.isMounted=!1,this.node=e}update(){}}class Ul extends nt{constructor(e){super(e),e.animationState||(e.animationState=Nl(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();se(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:i}=this.node.prevProps||{};e!==i&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)==null||e.call(this)}}let zl=0;class _l extends nt{constructor(){super(...arguments),this.id=zl++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:i}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const n=this.node.animationState.setActive("exit",!e);i&&!e&&n.then(()=>{i(this.id)})}mount(){const{register:e,onExitComplete:i}=this.node.presenceContext||{};i&&i(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const Hl={animation:{Feature:Ul},exit:{Feature:_l}};function Bt(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}function Wt(t){return{point:{x:t.pageX,y:t.pageY}}}const $l=t=>e=>di(e)&&t(e,Wt(e));function Mt(t,e,i,s){return Bt(t,e,$l(i),s)}function er({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function Kl({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Xl(t,e){if(!e)return t;const i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}const ir=1e-4,Yl=1-ir,Gl=1+ir,sr=.01,ql=0-sr,Zl=0+sr;function N(t){return t.max-t.min}function Jl(t,e,i){return Math.abs(t-e)<=i}function ss(t,e,i,s=.5){t.origin=s,t.originPoint=D(e.min,e.max,t.origin),t.scale=N(i)/N(e),t.translate=D(i.min,i.max,t.origin)-t.originPoint,(t.scale>=Yl&&t.scale<=Gl||isNaN(t.scale))&&(t.scale=1),(t.translate>=ql&&t.translate<=Zl||isNaN(t.translate))&&(t.translate=0)}function Ct(t,e,i,s){ss(t.x,e.x,i.x,s?s.originX:void 0),ss(t.y,e.y,i.y,s?s.originY:void 0)}function ns(t,e,i){t.min=i.min+e.min,t.max=t.min+N(e)}function Ql(t,e,i){ns(t.x,e.x,i.x),ns(t.y,e.y,i.y)}function rs(t,e,i){t.min=e.min-i.min,t.max=t.min+N(e)}function Et(t,e,i){rs(t.x,e.x,i.x),rs(t.y,e.y,i.y)}const os=()=>({translate:0,scale:1,origin:0,originPoint:0}),pt=()=>({x:os(),y:os()}),as=()=>({min:0,max:0}),k=()=>({x:as(),y:as()});function H(t){return[t("x"),t("y")]}function ge(t){return t===void 0||t===1}function je({scale:t,scaleX:e,scaleY:i}){return!ge(t)||!ge(e)||!ge(i)}function ot(t){return je(t)||nr(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function nr(t){return ls(t.x)||ls(t.y)}function ls(t){return t&&t!=="0%"}function te(t,e,i){const s=t-i,n=e*s;return i+n}function cs(t,e,i,s,n){return n!==void 0&&(t=te(t,n,s)),te(t,i,s)+e}function Ne(t,e=0,i=1,s,n){t.min=cs(t.min,e,i,s,n),t.max=cs(t.max,e,i,s,n)}function rr(t,{x:e,y:i}){Ne(t.x,e.translate,e.scale,e.originPoint),Ne(t.y,i.translate,i.scale,i.originPoint)}const us=.999999999999,hs=1.0000000000001;function tc(t,e,i,s=!1){const n=i.length;if(!n)return;e.x=e.y=1;let o,r;for(let a=0;a<n;a++){o=i[a],r=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&gt(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,rr(t,r)),s&&ot(o.latestValues)&&gt(t,o.latestValues))}e.x<hs&&e.x>us&&(e.x=1),e.y<hs&&e.y>us&&(e.y=1)}function mt(t,e){t.min=t.min+e,t.max=t.max+e}function fs(t,e,i,s,n=.5){const o=D(t.min,t.max,n);Ne(t,e,i,o,s)}function gt(t,e){fs(t.x,e.x,e.scaleX,e.scale,e.originX),fs(t.y,e.y,e.scaleY,e.scale,e.originY)}function or(t,e){return er(Xl(t.getBoundingClientRect(),e))}function ec(t,e,i){const s=or(t,i),{scroll:n}=e;return n&&(mt(s.x,n.offset.x),mt(s.y,n.offset.y)),s}const ar=({current:t})=>t?t.ownerDocument.defaultView:null,ds=(t,e)=>Math.abs(t-e);function ic(t,e){const i=ds(t.x,e.x),s=ds(t.y,e.y);return Math.sqrt(le(i,2)+le(s,2))}class lr{constructor(e,i,{transformPagePoint:s,contextWindow:n,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=ve(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=ic(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:p}=h,{timestamp:m}=B;this.history.push(C(T({},p),{timestamp:m}));const{onStart:g,onMove:y}=this.handlers;f||(g&&g(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=ye(f,this.transformPagePoint),R.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:p,resumeAnimation:m}=this.handlers;if(this.dragSnapToOrigin&&m&&m(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const g=ve(h.type==="pointercancel"?this.lastMoveEventInfo:ye(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,g),p&&p(h,g)},!di(e))return;this.dragSnapToOrigin=o,this.handlers=i,this.transformPagePoint=s,this.contextWindow=n||window;const r=Wt(e),a=ye(r,this.transformPagePoint),{point:l}=a,{timestamp:u}=B;this.history=[C(T({},l),{timestamp:u})];const{onSessionStart:c}=i;c&&c(e,ve(a,this.history)),this.removeListeners=It(Mt(this.contextWindow,"pointermove",this.handlePointerMove),Mt(this.contextWindow,"pointerup",this.handlePointerUp),Mt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),it(this.updatePoint)}}function ye(t,e){return e?{point:e(t.point)}:t}function ps(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ve({point:t},e){return{point:t,delta:ps(t,cr(e)),offset:ps(t,sc(e)),velocity:nc(e,.1)}}function sc(t){return t[0]}function cr(t){return t[t.length-1]}function nc(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null;const n=cr(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>X(e)));)i--;if(!s)return{x:0,y:0};const o=Y(n.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const r={x:(n.x-s.x)/o,y:(n.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function rc(t,{min:e,max:i},s){return e!==void 0&&t<e?t=s?D(e,t,s.min):Math.max(t,e):i!==void 0&&t>i&&(t=s?D(i,t,s.max):Math.min(t,i)),t}function ms(t,e,i){return{min:e!==void 0?t.min+e:void 0,max:i!==void 0?t.max+i-(t.max-t.min):void 0}}function oc(t,{top:e,left:i,bottom:s,right:n}){return{x:ms(t.x,i,n),y:ms(t.y,e,s)}}function gs(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function ac(t,e){return{x:gs(t.x,e.x),y:gs(t.y,e.y)}}function lc(t,e){let i=.5;const s=N(t),n=N(e);return n>s?i=Dt(e.min,e.max-s,t.min):s>n&&(i=Dt(t.min,t.max-n,e.min)),J(0,1,i)}function cc(t,e){const i={};return e.min!==void 0&&(i.min=e.min-t.min),e.max!==void 0&&(i.max=e.max-t.min),i}const We=.35;function uc(t=We){return t===!1?t=0:t===!0&&(t=We),{x:ys(t,"left","right"),y:ys(t,"top","bottom")}}function ys(t,e,i){return{min:vs(t,e),max:vs(t,i)}}function vs(t,e){return typeof t=="number"?t:t[e]||0}const hc=new WeakMap;class fc{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=k(),this.visualElement=e}start(e,{snapToCursor:i=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const n=c=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),i&&this.snapToCursor(Wt(c).point)},o=(c,h)=>{const{drag:f,dragPropagation:d,onDragStart:p}=this.getProps();if(f&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Va(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),H(g=>{let y=this.getAxisMotionValue(g).get()||0;if(G.test(y)){const{projection:S}=this.visualElement;if(S&&S.layout){const v=S.layout.layoutBox[g];v&&(y=N(v)*(parseFloat(y)/100))}}this.originPoint[g]=y}),p&&R.postRender(()=>p(c,h)),Be(this.visualElement,"transform");const{animationState:m}=this.visualElement;m&&m.setActive("whileDrag",!0)},r=(c,h)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:p,onDrag:m}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:g}=h;if(d&&this.currentDirection===null){this.currentDirection=dc(g),this.currentDirection!==null&&p&&p(this.currentDirection);return}this.updateAxis("x",h.point,g),this.updateAxis("y",h.point,g),this.visualElement.render(),m&&m(c,h)},a=(c,h)=>this.stop(c,h),l=()=>H(c=>{var h;return this.getAnimationState(c)==="paused"&&((h=this.getAxisMotionValue(c).animation)==null?void 0:h.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new lr(e,{onSessionStart:n,onStart:o,onMove:r,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:ar(this.visualElement)})}stop(e,i){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:n}=i;this.startAnimation(n);const{onDragEnd:o}=this.getProps();o&&R.postRender(()=>o(e,i))}cancel(){this.isDragging=!1;const{projection:e,animationState:i}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),i&&i.setActive("whileDrag",!1)}updateAxis(e,i,s){const{drag:n}=this.getProps();if(!s||!$t(e,n,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(r=rc(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var o;const{dragConstraints:e,dragElastic:i}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(o=this.visualElement.projection)==null?void 0:o.layout,n=this.constraints;e&&dt(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=oc(s.layoutBox,e):this.constraints=!1,this.elastic=uc(i),n!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&H(r=>{this.constraints!==!1&&this.getAxisMotionValue(r)&&(this.constraints[r]=cc(s.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!dt(e))return!1;const s=e.current,{projection:n}=this.visualElement;if(!n||!n.layout)return!1;const o=ec(s,n.root,this.visualElement.getTransformPagePoint());let r=ac(n.layout.layoutBox,o);if(i){const a=i(Kl(r));this.hasMutatedConstraints=!!a,a&&(r=er(a))}return r}startAnimation(e){const{drag:i,dragMomentum:s,dragElastic:n,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=H(c=>{if(!$t(c,i,this.currentDirection))return;let h=l&&l[c]||{};r&&(h={min:0,max:0});const f=n?200:1e6,d=n?40:1e7,p=T(T({type:"inertia",velocity:s?e[c]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10},o),h);return this.startAxisValueAnimation(c,p)});return Promise.all(u).then(a)}startAxisValueAnimation(e,i){const s=this.getAxisMotionValue(e);return Be(this.visualElement,e),s.start(bi(e,s,0,i,this.visualElement,!1))}stopAnimation(){H(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){H(e=>{var i;return(i=this.getAxisMotionValue(e).animation)==null?void 0:i.pause()})}getAnimationState(e){var i;return(i=this.getAxisMotionValue(e).animation)==null?void 0:i.state}getAxisMotionValue(e){const i=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps(),n=s[i];return n||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){H(i=>{const{drag:s}=this.getProps();if(!$t(i,s,this.currentDirection))return;const{projection:n}=this.visualElement,o=this.getAxisMotionValue(i);if(n&&n.layout){const{min:r,max:a}=n.layout.layoutBox[i];o.set(e[i]-D(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:i}=this.getProps(),{projection:s}=this.visualElement;if(!dt(i)||!s||!this.constraints)return;this.stopAnimation();const n={x:0,y:0};H(r=>{const a=this.getAxisMotionValue(r);if(a&&this.constraints!==!1){const l=a.get();n[r]=lc({min:l,max:l},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),H(r=>{if(!$t(r,e,null))return;const a=this.getAxisMotionValue(r),{min:l,max:u}=this.constraints[r];a.set(D(l,u,n[r]))})}addListeners(){if(!this.visualElement.current)return;hc.set(this.visualElement,this);const e=this.visualElement.current,i=Mt(e,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();dt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,o=n.addEventListener("measure",s);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),R.read(s);const r=Bt(window,"resize",()=>this.scalePositionWithinConstraints()),a=n.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(H(c=>{const h=this.getAxisMotionValue(c);h&&(this.originPoint[c]+=l[c].translate,h.set(h.get()+l[c].translate))}),this.visualElement.render())});return()=>{r(),i(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:i=!1,dragDirectionLock:s=!1,dragPropagation:n=!1,dragConstraints:o=!1,dragElastic:r=We,dragMomentum:a=!0}=e;return C(T({},e),{drag:i,dragDirectionLock:s,dragPropagation:n,dragConstraints:o,dragElastic:r,dragMomentum:a})}}function $t(t,e,i){return(e===!0||e===t)&&(i===null||i===t)}function dc(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}class pc extends nt{constructor(e){super(e),this.removeGroupControls=$,this.removeListeners=$,this.controls=new fc(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||$}unmount(){this.removeGroupControls(),this.removeListeners()}}const Ts=t=>(e,i)=>{t&&R.postRender(()=>t(e,i))};class mc extends nt{constructor(){super(...arguments),this.removePointerDownListener=$}onPointerDown(e){this.session=new lr(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ar(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:i,onPan:s,onPanEnd:n}=this.node.getProps();return{onSessionStart:Ts(e),onStart:Ts(i),onMove:s,onEnd:(o,r)=>{delete this.session,n&&R.postRender(()=>n(o,r))}}}mount(){this.removePointerDownListener=Mt(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Gt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function xs(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const bt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(P.test(t))t=parseFloat(t);else return t;const i=xs(t,e.target.x),s=xs(t,e.target.y);return`${i}% ${s}%`}},gc={correct:(t,{treeScale:e,projectionDelta:i})=>{const s=t,n=st.parse(t);if(n.length>5)return s;const o=st.createTransformer(t),r=typeof n[0]!="number"?1:0,a=i.x.scale*e.x,l=i.y.scale*e.y;n[0+r]/=a,n[1+r]/=l;const u=D(a,l,.5);return typeof n[2+r]=="number"&&(n[2+r]/=u),typeof n[3+r]=="number"&&(n[3+r]/=u),o(n)}};class yc extends w.Component{componentDidMount(){const{visualElement:e,layoutGroup:i,switchLayoutGroup:s,layoutId:n}=this.props,{projection:o}=e;el(vc),o&&(i.group&&i.group.add(o),s&&s.register&&n&&s.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions(C(T({},o.options),{onExitComplete:()=>this.safeToRemove()}))),Gt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:i,visualElement:s,drag:n,isPresent:o}=this.props,{projection:r}=s;return r&&(r.isPresent=o,n||e.layoutDependency!==i||i===void 0||e.isPresent!==o?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||R.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),fi.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:i,switchLayoutGroup:s}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),i&&i.group&&i.group.remove(n),s&&s.deregister&&s.deregister(n))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function ur(t){const[e,i]=In(),s=w.useContext(_e);return Z.jsx(yc,C(T({},t),{layoutGroup:s,switchLayoutGroup:w.useContext(zn),isPresent:e,safeToRemove:i}))}const vc={borderRadius:C(T({},bt),{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:bt,borderTopRightRadius:bt,borderBottomLeftRadius:bt,borderBottomRightRadius:bt,boxShadow:gc};function Tc(t,e,i){const s=I(t)?t:yt(t);return s.start(bi("",s,e,i)),s.animation}const xc=(t,e)=>t.depth-e.depth;class Sc{constructor(){this.children=[],this.isDirty=!1}add(e){Ke(this.children,e),this.isDirty=!0}remove(e){Xe(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(xc),this.isDirty=!1,this.children.forEach(e)}}function wc(t,e){const i=U.now(),s=({timestamp:n})=>{const o=n-i;o>=e&&(it(s),t(o-e))};return R.setup(s,!0),()=>it(s)}const hr=["TopLeft","TopRight","BottomLeft","BottomRight"],bc=hr.length,Ss=t=>typeof t=="string"?parseFloat(t):t,ws=t=>typeof t=="number"||P.test(t);function Pc(t,e,i,s,n,o){var r,a,l,u;n?(t.opacity=D(0,(r=i.opacity)!=null?r:1,Ac(s)),t.opacityExit=D((a=e.opacity)!=null?a:1,0,Vc(s))):o&&(t.opacity=D((l=e.opacity)!=null?l:1,(u=i.opacity)!=null?u:1,s));for(let c=0;c<bc;c++){const h=`border${hr[c]}Radius`;let f=bs(e,h),d=bs(i,h);if(f===void 0&&d===void 0)continue;f||(f=0),d||(d=0),f===0||d===0||ws(f)===ws(d)?(t[h]=Math.max(D(Ss(f),Ss(d),s),0),(G.test(d)||G.test(f))&&(t[h]+="%")):t[h]=d}(e.rotate||i.rotate)&&(t.rotate=D(e.rotate||0,i.rotate||0,s))}function bs(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const Ac=fr(0,.5,nn),Vc=fr(.5,.95,$);function fr(t,e,i){return s=>s<t?0:s>e?1:i(Dt(t,e,s))}function Ps(t,e){t.min=e.min,t.max=e.max}function _(t,e){Ps(t.x,e.x),Ps(t.y,e.y)}function As(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Vs(t,e,i,s,n){return t-=e,t=te(t,1/i,s),n!==void 0&&(t=te(t,1/n,s)),t}function Mc(t,e=0,i=1,s=.5,n,o=t,r=t){if(G.test(e)&&(e=parseFloat(e),e=D(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=D(o.min,o.max,s);t===o&&(a-=e),t.min=Vs(t.min,e,i,a,n),t.max=Vs(t.max,e,i,a,n)}function Ms(t,e,[i,s,n],o,r){Mc(t,e[i],e[s],e[n],e.scale,o,r)}const Cc=["x","scaleX","originX"],Ec=["y","scaleY","originY"];function Cs(t,e,i,s){Ms(t.x,e,Cc,i?i.x:void 0,s?s.x:void 0),Ms(t.y,e,Ec,i?i.y:void 0,s?s.y:void 0)}function Es(t){return t.translate===0&&t.scale===1}function dr(t){return Es(t.x)&&Es(t.y)}function Ds(t,e){return t.min===e.min&&t.max===e.max}function Dc(t,e){return Ds(t.x,e.x)&&Ds(t.y,e.y)}function Rs(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function pr(t,e){return Rs(t.x,e.x)&&Rs(t.y,e.y)}function Ls(t){return N(t.x)/N(t.y)}function ks(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Rc{constructor(){this.members=[]}add(e){Ke(this.members,e),e.scheduleRender()}remove(e){if(Xe(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const i=this.members[this.members.length-1];i&&this.promote(i)}}relegate(e){const i=this.members.findIndex(n=>e===n);if(i===0)return!1;let s;for(let n=i;n>=0;n--){const o=this.members[n];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(e,i){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,i&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:n}=e.options;n===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:i,resumingFrom:s}=e;i.onExitComplete&&i.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Lc(t,e,i){let s="";const n=t.x.translate/e.x,o=t.y.translate/e.y,r=(i==null?void 0:i.z)||0;if((n||o||r)&&(s=`translate3d(${n}px, ${o}px, ${r}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){const{transformPerspective:u,rotate:c,rotateX:h,rotateY:f,skewX:d,skewY:p}=i;u&&(s=`perspective(${u}px) ${s}`),c&&(s+=`rotate(${c}deg) `),h&&(s+=`rotateX(${h}deg) `),f&&(s+=`rotateY(${f}deg) `),d&&(s+=`skewX(${d}deg) `),p&&(s+=`skewY(${p}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(s+=`scale(${a}, ${l})`),s||"none"}const Te=["","X","Y","Z"],kc={visibility:"hidden"},Fc=1e3;let Oc=0;function xe(t,e,i,s){const{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function mr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const i=Zn(e);if(window.MotionHasOptimisedAnimation(i,"transform")){const{layout:n,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(i,"transform",R,!(n||o))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&mr(s)}function gr({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(r={},a=e==null?void 0:e()){this.id=Oc++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(jc),this.nodes.forEach(_c),this.nodes.forEach(Hc),this.nodes.forEach(Nc)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Sc)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new qe),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const l=this.eventHandlers.get(r);l&&l.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r){if(this.instance)return;this.isSVG=Bn(r)&&!La(r),this.instance=r;const{layoutId:a,layout:l,visualElement:u}=this.options;if(u&&!u.current&&u.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||a)&&(this.isLayoutDirty=!0),t){let c;const h=()=>this.root.updateBlockedByResize=!1;t(r,()=>{this.root.updateBlockedByResize=!0,c&&c(),c=wc(h,250),Gt.hasAnimatedSinceResize&&(Gt.hasAnimatedSinceResize=!1,this.nodes.forEach(Os))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&u&&(a||l)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:h,hasRelativeLayoutChanged:f,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const p=this.options.transition||u.getDefaultTransition()||Gc,{onLayoutAnimationStart:m,onLayoutAnimationComplete:g}=u.getProps(),y=!this.targetLayout||!pr(this.targetLayout,d),S=!h&&f;if(this.options.layoutRoot||this.resumeFrom||S||h&&(y||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const v=C(T({},ui(p,"layout")),{onPlay:m,onComplete:g});(u.shouldReduceMotion||this.options.layoutRoot)&&(v.delay=0,v.type=!1),this.startAnimation(v),this.setAnimationOrigin(c,S)}else h||Os(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),it(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach($c),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&mr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const h=this.path[c];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Fs);return}this.isUpdating||this.nodes.forEach(Uc),this.isUpdating=!1,this.nodes.forEach(zc),this.nodes.forEach(Bc),this.nodes.forEach(Ic),this.clearAllSnapshots();const a=U.now();B.delta=J(0,1e3/60,a-B.timestamp),B.timestamp=a,B.isProcessing=!0,ue.update.process(B),ue.preRender.process(B),ue.render.process(B),B.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,fi.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Wc),this.sharedNodes.forEach(Kc)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,R.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){R.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!N(this.snapshot.measuredBox.x)&&!N(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=k(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a&&this.instance){const l=s(this.instance);this.scroll={animationId:this.root.animationId,phase:r,isRoot:l,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!n)return;const r=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!dr(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;r&&this.instance&&(a||ot(this.latestValues)||c)&&(n(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return r&&(l=this.removeTransform(l)),qc(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var u;const{visualElement:r}=this.options;if(!r)return k();const a=r.measureViewportBox();if(!(((u=this.scroll)==null?void 0:u.wasRoot)||this.path.some(Zc))){const{scroll:c}=this.root;c&&(mt(a.x,c.offset.x),mt(a.y,c.offset.y))}return a}removeElementScroll(r){var l;const a=k();if(_(a,r),(l=this.scroll)!=null&&l.wasRoot)return a;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:h,options:f}=c;c!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&_(a,r),mt(a.x,h.offset.x),mt(a.y,h.offset.y))}return a}applyTransform(r,a=!1){const l=k();_(l,r);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&gt(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),ot(c.latestValues)&&gt(l,c.latestValues)}return ot(this.latestValues)&&gt(l,this.latestValues),l}removeTransform(r){const a=k();_(a,r);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!ot(u.latestValues))continue;je(u.latestValues)&&u.updateSnapshot();const c=k(),h=u.measurePageBox();_(c,h),Cs(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return ot(this.latestValues)&&Cs(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options=C(T(T({},this.options),r),{crossfade:r.crossfade!==void 0?r.crossfade:!0})}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==B.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var f;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==a;if(!(r||l&&this.isSharedProjectionDirty||this.isProjectionDirty||(f=this.parent)!=null&&f.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:c,layoutId:h}=this.options;if(!(!this.layout||!(c||h))){if(this.resolvedRelativeTargetAt=B.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=k(),this.relativeTargetOrigin=k(),Et(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),_(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=k(),this.targetWithTransforms=k()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Ql(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):_(this.target,this.layout.layoutBox),rr(this.target,this.targetDelta)):_(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=k(),this.relativeTargetOrigin=k(),Et(this.relativeTargetOrigin,this.target,d.target),_(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||je(this.parent.latestValues)||nr(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var p;const r=this.getLead(),a=!!this.resumingFrom||this!==r;let l=!0;if((this.isProjectionDirty||(p=this.parent)!=null&&p.isProjectionDirty)&&(l=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===B.timestamp&&(l=!1),l)return;const{layout:u,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||c))return;_(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,f=this.treeScale.y;tc(this.layoutCorrected,this.treeScale,this.path,a),r.layout&&!r.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(r.target=r.layout.layoutBox,r.targetWithTransforms=k());const{target:d}=r;if(!d){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(As(this.prevProjectionDelta.x,this.projectionDelta.x),As(this.prevProjectionDelta.y,this.projectionDelta.y)),Ct(this.projectionDelta,this.layoutCorrected,d,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==f||!ks(this.projectionDelta.x,this.prevProjectionDelta.x)||!ks(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",d))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){var a;if((a=this.options.visualElement)==null||a.scheduleRender(),r){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=pt(),this.projectionDelta=pt(),this.projectionDeltaWithTransform=pt()}setAnimationOrigin(r,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c=T({},this.latestValues),h=pt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=k(),d=l?l.source:void 0,p=this.layout?this.layout.source:void 0,m=d!==p,g=this.getStack(),y=!g||g.members.length<=1,S=!!(m&&!y&&this.options.crossfade===!0&&!this.path.some(Yc));this.animationProgress=0;let v;this.mixTargetDelta=V=>{const x=V/1e3;Bs(h.x,r.x,x),Bs(h.y,r.y,x),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Et(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Xc(this.relativeTarget,this.relativeTargetOrigin,f,x),v&&Dc(this.relativeTarget,v)&&(this.isProjectionDirty=!1),v||(v=k()),_(v,this.relativeTarget)),m&&(this.animationValues=c,Pc(c,u,this.latestValues,x,S,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=x},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){var a,l,u;this.notifyListeners("animationStart"),(a=this.currentAnimation)==null||a.stop(),(u=(l=this.resumingFrom)==null?void 0:l.currentAnimation)==null||u.stop(),this.pendingAnimation&&(it(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=R.update(()=>{Gt.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=yt(0)),this.currentAnimation=Tc(this.motionValue,[0,1e3],C(T({},r),{velocity:0,isSync:!0,onUpdate:c=>{this.mixTargetDelta(c),r.onUpdate&&r.onUpdate(c)},onStop:()=>{},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}})),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Fc),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=r;if(!(!a||!l||!u)){if(this!==r&&this.layout&&u&&yr(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||k();const h=N(this.layout.layoutBox.x);l.x.min=r.target.x.min,l.x.max=l.x.min+h;const f=N(this.layout.layoutBox.y);l.y.min=r.target.y.min,l.y.max=l.y.min+f}_(a,l),gt(a,c),Ct(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new Rc),this.sharedNodes.get(r).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var a;const{layoutId:r}=this.options;return r?((a=this.getStack())==null?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:r}=this.options;return r?(a=this.getStack())==null?void 0:a.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetSkewAndRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:l}=r;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&xe("z",r,u,this.animationValues);for(let c=0;c<Te.length;c++)xe(`rotate${Te[c]}`,r,u,this.animationValues),xe(`skew${Te[c]}`,r,u,this.animationValues);r.render();for(const c in u)r.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);r.scheduleRender()}getProjectionStyles(r){var d,p;if(!this.instance||this.isSVG)return;if(!this.isVisible)return kc;const a={visibility:""},l=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,a.opacity="",a.pointerEvents=Yt(r==null?void 0:r.pointerEvents)||"",a.transform=l?l(this.latestValues,""):"none",a;const u=this.getLead();if(!this.projectionDelta||!this.layout||!u.target){const m={};return this.options.layoutId&&(m.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,m.pointerEvents=Yt(r==null?void 0:r.pointerEvents)||""),this.hasProjected&&!ot(this.latestValues)&&(m.transform=l?l({},""):"none",this.hasProjected=!1),m}const c=u.animationValues||u.latestValues;this.applyTransformsToTarget(),a.transform=Lc(this.projectionDeltaWithTransform,this.treeScale,c),l&&(a.transform=l(c,a.transform));const{x:h,y:f}=this.projectionDelta;a.transformOrigin=`${h.origin*100}% ${f.origin*100}% 0`,u.animationValues?a.opacity=u===this?(p=(d=c.opacity)!=null?d:this.latestValues.opacity)!=null?p:1:this.preserveOpacity?this.latestValues.opacity:c.opacityExit:a.opacity=u===this?c.opacity!==void 0?c.opacity:"":c.opacityExit!==void 0?c.opacityExit:0;for(const m in Ft){if(c[m]===void 0)continue;const{correct:g,applyTo:y,isCSSVariable:S}=Ft[m],v=a.transform==="none"?c[m]:g(c[m],u);if(y){const V=y.length;for(let x=0;x<V;x++)a[y[x]]=v}else S?this.options.visualElement.renderState.vars[m]=v:a[m]=v}return this.options.layoutId&&(a.pointerEvents=u===this?Yt(r==null?void 0:r.pointerEvents)||"":"none"),a}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)==null?void 0:a.stop()}),this.root.nodes.forEach(Fs),this.root.sharedNodes.clear()}}}function Bc(t){t.updateLayout()}function Ic(t){var i;const e=((i=t.resumeFrom)==null?void 0:i.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:n}=t.layout,{animationType:o}=t.options,r=e.source!==t.layout.source;o==="size"?H(h=>{const f=r?e.measuredBox[h]:e.layoutBox[h],d=N(f);f.min=s[h].min,f.max=f.min+d}):yr(o,e.layoutBox,s)&&H(h=>{const f=r?e.measuredBox[h]:e.layoutBox[h],d=N(s[h]);f.max=f.min+d,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+d)});const a=pt();Ct(a,s,e.layoutBox);const l=pt();r?Ct(l,t.applyTransform(n,!0),e.measuredBox):Ct(l,s,e.layoutBox);const u=!dr(a);let c=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const p=k();Et(p,e.layoutBox,f.layoutBox);const m=k();Et(m,s,d.layoutBox),pr(p,m)||(c=!0),h.options.layoutRoot&&(t.relativeTarget=m,t.relativeTargetOrigin=p,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:e,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeLayoutChanged:c})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function jc(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Nc(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Wc(t){t.clearSnapshot()}function Fs(t){t.clearMeasurements()}function Uc(t){t.isLayoutDirty=!1}function zc(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Os(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function _c(t){t.resolveTargetDelta()}function Hc(t){t.calcProjection()}function $c(t){t.resetSkewAndRotation()}function Kc(t){t.removeLeadSnapshot()}function Bs(t,e,i){t.translate=D(e.translate,0,i),t.scale=D(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function Is(t,e,i,s){t.min=D(e.min,i.min,s),t.max=D(e.max,i.max,s)}function Xc(t,e,i,s){Is(t.x,e.x,i.x,s),Is(t.y,e.y,i.y,s)}function Yc(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const Gc={duration:.45,ease:[.4,0,.1,1]},js=t=>typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Ns=js("applewebkit/")&&!js("chrome/")?Math.round:$;function Ws(t){t.min=Ns(t.min),t.max=Ns(t.max)}function qc(t){Ws(t.x),Ws(t.y)}function yr(t,e,i){return t==="position"||t==="preserve-aspect"&&!Jl(Ls(e),Ls(i),.2)}function Zc(t){var e;return t!==t.root&&((e=t.scroll)==null?void 0:e.wasRoot)}const Jc=gr({attachResizeListener:(t,e)=>Bt(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Se={current:void 0},vr=gr({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Se.current){const t=new Jc({});t.mount(window),t.setOptions({layoutScroll:!0}),Se.current=t}return Se.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Qc={pan:{Feature:mc},drag:{Feature:pc,ProjectionNode:vr,MeasureLayout:ur}};function Us(t,e,i){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",i==="Start");const n="onHover"+i,o=s[n];o&&R.postRender(()=>o(e,Wt(e)))}class tu extends nt{mount(){const{current:e}=this.node;e&&(this.unmount=Ma(e,(i,s)=>(Us(this.node,s,"Start"),n=>Us(this.node,n,"End"))))}unmount(){}}class eu extends nt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(i){e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=It(Bt(this.node.current,"focus",()=>this.onFocus()),Bt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function zs(t,e,i){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",i==="Start");const n="onTap"+(i==="End"?"":i),o=s[n];o&&R.postRender(()=>o(e,Wt(e)))}class iu extends nt{mount(){const{current:e}=this.node;e&&(this.unmount=Ra(e,(i,s)=>(zs(this.node,s,"Start"),(n,{success:o})=>zs(this.node,n,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Ue=new WeakMap,we=new WeakMap,su=t=>{const e=Ue.get(t.target);e&&e(t)},nu=t=>{t.forEach(su)};function ru(i){var s=i,{root:t}=s,e=z(s,["root"]);const n=t||document;we.has(n)||we.set(n,{});const o=we.get(n),r=JSON.stringify(e);return o[r]||(o[r]=new IntersectionObserver(nu,T({root:t},e))),o[r]}function ou(t,e,i){const s=ru(e);return Ue.set(t,i),s.observe(t),()=>{Ue.delete(t),s.unobserve(t)}}const au={some:0,all:1};class lu extends nt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:i,margin:s,amount:n="some",once:o}=e,r={root:i?i.current:void 0,rootMargin:s,threshold:typeof n=="number"?n:au[n]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:h}=this.node.getProps(),f=u?c:h;f&&f(l)};return ou(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver=="undefined")return;const{props:e,prevProps:i}=this.node;["amount","margin","root"].some(cu(e,i))&&this.startObserver()}unmount(){}}function cu({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}const uu={inView:{Feature:lu},tap:{Feature:iu},focus:{Feature:eu},hover:{Feature:tu}},hu={layout:{ProjectionNode:vr,MeasureLayout:ur}},ze={current:null},Tr={current:!1};function fu(){if(Tr.current=!0,!!$e)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ze.current=t.matches;t.addListener(e),e()}else ze.current=!1}const du=new WeakMap;function pu(t,e,i){for(const s in e){const n=e[s],o=i[s];if(I(n))t.addValue(s,n);else if(I(o))t.addValue(s,yt(n,{owner:t}));else if(o!==n)if(t.hasValue(s)){const r=t.getValue(s);r.liveStyle===!0?r.jump(n):r.hasAnimated||r.set(n)}else{const r=t.getStaticValue(s);t.addValue(s,yt(r!==void 0?r:n,{owner:t}))}}for(const s in i)e[s]===void 0&&t.removeValue(s);return e}const _s=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class mu{scrapeMotionValuesFromProps(e,i,s){return{}}constructor({parent:e,props:i,presenceContext:s,reducedMotionConfig:n,blockInitialAnimation:o,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=li,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const d=U.now();this.renderScheduledAt<d&&(this.renderScheduledAt=d,R.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=r;this.latestValues=l,this.baseTarget=T({},l),this.initialValues=i.initial?T({},l):{},this.renderState=u,this.parent=e,this.props=i,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=ne(i),this.isVariantNode=Wn(i),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const f=this.scrapeMotionValuesFromProps(i,{},this),{willChange:c}=f,h=z(f,["willChange"]);for(const d in h){const p=h[d];l[d]!==void 0&&I(p)&&p.set(l[d],!1)}}mount(e){this.current=e,du.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((i,s)=>this.bindToMotionValue(s,i)),Tr.current||fu(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ze.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),it(this.notifyUpdate),it(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const i=this.features[e];i&&(i.unmount(),i.isMounted=!1)}this.current=null}bindToMotionValue(e,i){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=St.has(e);s&&this.onBindTransform&&this.onBindTransform();const n=i.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&R.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=i.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,i)),this.valueSubscriptions.set(e,()=>{n(),o(),r&&r(),i.owner&&i.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in vt){const i=vt[e];if(!i)continue;const{isEnabled:s,Feature:n}=i;if(!this.features[e]&&n&&s(this.props)&&(this.features[e]=new n(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):k()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,i){this.latestValues[e]=i}update(e,i){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=i;for(let s=0;s<_s.length;s++){const n=_s[s];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const o="on"+n,r=e[o];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=pu(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const i=this.getClosestVariantNode();if(i)return i.variantChildren&&i.variantChildren.add(e),()=>i.variantChildren.delete(e)}addValue(e,i){const s=this.values.get(e);i!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,i),this.values.set(e,i),this.latestValues[e]=i.get())}removeValue(e){this.values.delete(e);const i=this.valueSubscriptions.get(e);i&&(i(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,i){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&i!==void 0&&(s=yt(i===null?void 0:i,{owner:this}),this.addValue(e,s)),s}readValue(e,i){var n;let s=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(n=this.getBaseTargetFromProps(this.props,e))!=null?n:this.readValueFromInstance(this.current,e,this.options);return s!=null&&(typeof s=="string"&&(Xs(s)||Gs(s))?s=parseFloat(s):!Fa(s)&&st.test(i)&&(s=Rn(e,i)),this.setBaseTarget(e,I(s)?s.get():s)),I(s)?s.get():s}setBaseTarget(e,i){this.baseTarget[e]=i}getBaseTarget(e){var o;const{initial:i}=this.props;let s;if(typeof i=="string"||typeof i=="object"){const r=Si(this.props,i,(o=this.presenceContext)==null?void 0:o.custom);r&&(s=r[e])}if(i&&s!==void 0)return s;const n=this.getBaseTargetFromProps(this.props,e);return n!==void 0&&!I(n)?n:this.initialValues[e]!==void 0&&s===void 0?void 0:this.baseTarget[e]}on(e,i){return this.events[e]||(this.events[e]=new qe),this.events[e].add(i)}notify(e,...i){this.events[e]&&this.events[e].notify(...i)}}class xr extends mu{constructor(){super(...arguments),this.KeyframeResolver=wa}sortInstanceNodePosition(e,i){return e.compareDocumentPosition(i)&2?1:-1}getBaseTargetFromProps(e,i){return e.style?e.style[i]:void 0}removeValueFromRenderState(e,{vars:i,style:s}){delete i[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;I(e)&&(this.childSubscription=e.on("change",i=>{this.current&&(this.current.textContent=`${i}`)}))}}function Sr(t,{style:e,vars:i},s,n){Object.assign(t.style,e,n&&n.getProjectionStyles(s));for(const o in i)t.style.setProperty(o,i[o])}function gu(t){return window.getComputedStyle(t)}class yu extends xr{constructor(){super(...arguments),this.type="html",this.renderInstance=Sr}readValueFromInstance(e,i){var s;if(St.has(i))return(s=this.projection)!=null&&s.isProjecting?Ee(i):Uo(e,i);{const n=gu(e),o=(Qe(i)?n.getPropertyValue(i):n[i])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(e,{transformPagePoint:i}){return or(e,i)}build(e,i,s){vi(e,i,s.transformTemplate)}scrapeMotionValuesFromProps(e,i,s){return wi(e,i,s)}}const wr=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function vu(t,e,i,s){Sr(t,e,void 0,s);for(const n in e.attrs)t.setAttribute(wr.has(n)?n:yi(n),e.attrs[n])}class Tu extends xr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=k}getBaseTargetFromProps(e,i){return e[i]}readValueFromInstance(e,i){if(St.has(i)){const s=Dn(i);return s&&s.default||0}return i=wr.has(i)?i:yi(i),e.getAttribute(i)}scrapeMotionValuesFromProps(e,i,s){return qn(e,i,s)}build(e,i,s){Kn(e,i,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(e,i,s,n){vu(e,i,s,n)}mount(e){this.isSVGTag=Yn(e.tagName),super.mount(e)}}const xu=(t,e)=>xi(t)?new Tu(e):new yu(e,{allowProjection:t!==w.Fragment}),Su=vl(T(T(T(T({},Hl),uu),Qc),hu),xu),Eu=_a(Su);export{Mu as A,Vu as L,Z as j,Eu as m,w as r};
