import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Sparkles } from 'lucide-react'

interface LoadingScreenProps {
  onComplete: () => void
}

const LoadingScreen = ({ onComplete }: LoadingScreenProps) => {
  const [progress, setProgress] = useState(0)
  const [isComplete, setIsComplete] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer)
          setTimeout(() => {
            setIsComplete(true)
            setTimeout(onComplete, 800)
          }, 500)
          return 100
        }
        return prev + Math.random() * 15 + 5
      })
    }, 100)

    return () => clearInterval(timer)
  }, [onComplete])

  const containerVariants = {
    hidden: { opacity: 1 },
    exit: {
      opacity: 0,
      scale: 1.1,
      transition: {
        duration: 0.8,
        ease: 'easeInOut',
      },
    },
  }

  const logoVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 1,
        ease: 'easeOut',
      },
    },
    pulse: {
      scale: [1, 1.05, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  }

  const sparkleVariants = {
    animate: {
      rotate: 360,
      scale: [1, 1.2, 1],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  }

  return (
    <AnimatePresence>
      {!isComplete && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="fixed inset-0 z-50 flex items-center justify-center"
        >
          {/* Luxury Loading Background with Depth */}
          <div className="absolute inset-0">
            {/* High-end fashion boutique background */}
            <div
              className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
              style={{
                backgroundImage: `url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80')`
              }}
            />
            {/* Luxury fashion accessories for depth */}
            <div
              className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10 mix-blend-overlay"
              style={{
                backgroundImage: `url('https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80')`
              }}
            />
            {/* Gradient overlay for elegance */}
            <div className="absolute inset-0 bg-gradient-to-br from-cream-100/95 via-cream-50/90 to-rose-50/95" />
            {/* Subtle radial focus */}
            <div className="absolute inset-0 bg-radial-gradient from-transparent via-transparent to-cream-200/40" />
          </div>

          {/* Enhanced Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <motion.div
              variants={sparkleVariants}
              animate="animate"
              className="absolute top-20 left-20 text-gold-300 opacity-40"
            >
              <Sparkles size={40} />
            </motion.div>
            <motion.div
              variants={sparkleVariants}
              animate="animate"
              style={{ animationDelay: '1s' }}
              className="absolute top-40 right-32 text-rose-300 opacity-40"
            >
              <Sparkles size={30} />
            </motion.div>
            <motion.div
              variants={sparkleVariants}
              animate="animate"
              style={{ animationDelay: '2s' }}
              className="absolute bottom-32 left-32 text-gold-300 opacity-40"
            >
              <Sparkles size={35} />
            </motion.div>
            <motion.div
              variants={sparkleVariants}
              animate="animate"
              style={{ animationDelay: '0.5s' }}
              className="absolute bottom-20 right-20 text-rose-300 opacity-40"
            >
              <Sparkles size={25} />
            </motion.div>
          </div>

          {/* Main Content */}
          <div className="text-center relative z-10">
            {/* Logo */}
            <motion.div
              variants={logoVariants}
              initial="hidden"
              animate={progress < 100 ? 'visible' : 'pulse'}
              className="mb-10"
            >
              <div className="bg-white/30 backdrop-blur-luxury rounded-3xl p-8 shadow-depth-lg border border-white/20 max-w-md mx-auto">
                <div className="w-24 h-24 bg-gradient-luxury from-gold-500 to-rose-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-depth-lg">
                  <span className="text-white font-bold text-3xl">FA</span>
                </div>
                <h1 className="text-4xl font-serif font-bold text-charcoal-900 mb-3">
                  Fashionistas Attic
                </h1>
                <p className="text-gold-600 font-script text-xl">Boutique</p>
              </div>
            </motion.div>

            {/* Enhanced Loading Bar */}
            <div className="w-80 mx-auto mb-8">
              <div className="bg-white/30 backdrop-blur-luxury rounded-2xl p-6 shadow-depth border border-white/20">
                <div className="w-full bg-charcoal-200 rounded-full h-3 overflow-hidden shadow-inner">
                  <motion.div
                    className="h-full bg-gradient-luxury from-gold-500 to-rose-500 rounded-full shadow-lg"
                    initial={{ width: 0 }}
                    animate={{ width: `${Math.min(progress, 100)}%` }}
                    transition={{ duration: 0.3, ease: 'easeOut' }}
                  />
                </div>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="text-charcoal-700 font-medium text-sm mt-4"
                >
                  {progress < 30 && '✨ Curating luxury collections...'}
                  {progress >= 30 && progress < 60 && '🎪 Preparing exclusive events...'}
                  {progress >= 60 && progress < 90 && '💎 Polishing the boutique experience...'}
                  {progress >= 90 && '🌟 Welcome to luxury fashion!'}
                </motion.p>
                <div className="text-charcoal-500 text-xs mt-2">{Math.round(progress)}%</div>
              </div>
            </div>

            {/* Floating Elements */}
            <motion.div
              animate={{
                y: [-10, 10, -10],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
              className="text-charcoal-400 text-xs"
            >
              Premium fashion • Affordable luxury • Community focused
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default LoadingScreen
