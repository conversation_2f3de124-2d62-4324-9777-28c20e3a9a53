import { motion } from 'framer-motion'
import { Calendar, MapPin, Clock, Users, ArrowR<PERSON>, Sparkles } from 'lucide-react'

const PopUpEvents = () => {
  const events = [
    {
      id: 1,
      title: 'Holiday Fashion Showcase',
      date: 'December 15, 2024',
      time: '10:00 AM - 6:00 PM',
      location: 'Downtown Community Center',
      address: '123 Main Street, City Center',
      description: 'Discover our exclusive holiday collection featuring elegant dresses, cozy winter wear, and festive accessories for the whole family.',
      attendees: 150,
      featured: true,
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=400&fit=crop&crop=center',
    },
    {
      id: 2,
      title: 'New Year Style Reset',
      date: 'January 12, 2025',
      time: '11:00 AM - 5:00 PM',
      location: 'Riverside Mall',
      address: '456 Shopping Blvd, Riverside',
      description: 'Start the new year with fresh style! Browse our latest spring preview collection and get styling tips from our fashion experts.',
      attendees: 120,
      featured: false,
      image: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=600&h=400&fit=crop&crop=center',
    },
    {
      id: 3,
      title: 'Valentine\'s Day Special',
      date: 'February 10, 2025',
      time: '12:00 PM - 7:00 PM',
      location: 'City Park Pavilion',
      address: '789 Park Avenue, Garden District',
      description: 'Find the perfect outfit for your special day. Romantic styles, elegant accessories, and exclusive Valentine\'s Day discounts.',
      attendees: 200,
      featured: false,
      image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=600&h=400&fit=crop&crop=center',
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const cardVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: 'easeOut',
      },
    },
  }

  return (
    <section id="events" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Elegant Event Background with Depth */}
      <div className="absolute inset-0">
        {/* Luxury event space background */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-12"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1492684223066-81342ee5ff30?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80')`
          }}
        />
        {/* Fashion boutique interior for depth */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-8 mix-blend-soft-light"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80')`
          }}
        />
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-cream-50/95 to-white/90" />
        {/* Subtle texture for luxury feel */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gold-50/20 to-transparent" />
      </div>

      {/* Floating decorative element with depth */}
      <motion.div
        animate={{ rotate: 360, scale: [1, 1.1, 1] }}
        transition={{ duration: 50, repeat: Infinity, ease: 'linear' }}
        className="absolute top-20 right-10 w-32 h-32 border border-gold-200 rounded-full opacity-15 shadow-lg backdrop-blur-sm"
      ></motion.div>

      <div className="container-luxury relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="text-gold-500" size={24} />
            <span className="text-gold-600 font-medium uppercase tracking-wider text-sm">
              Exclusive Events
            </span>
            <Sparkles className="text-gold-500" size={24} />
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-serif font-bold text-charcoal-900 mb-6">
            Upcoming{' '}
            <span className="text-gradient">Pop-Up</span>
            {' '}Events
          </h2>
          <div className="bg-white/30 backdrop-blur-luxury rounded-2xl p-6 shadow-depth border border-white/20 max-w-3xl mx-auto">
            <p className="text-lg text-charcoal-700 font-medium">
              Join us at our <span className="text-gold-700 font-semibold">exclusive pop-up events</span> where fashion meets community.
              Experience our collections in person and enjoy <span className="text-rose-700 font-semibold">special event-only discounts</span>.
            </p>
          </div>
        </motion.div>

        {/* Events Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8"
        >
          {events.map((event, index) => (
            <motion.div
              key={event.id}
              variants={cardVariants}
              className={`luxury-card group cursor-pointer shadow-depth-lg backdrop-blur-luxury bg-white/40 border border-white/30 hover:bg-white/60 transition-all duration-500 ${
                event.featured ? 'lg:col-span-2 lg:row-span-1' : ''
              }`}
            >
              {/* Event Image */}
              <div className="relative overflow-hidden rounded-xl mb-6">
                <img
                  src={event.image}
                  alt={event.title}
                  className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                {event.featured && (
                  <div className="absolute top-4 left-4">
                    <span className="px-3 py-1 bg-gold-500 text-white text-xs font-medium rounded-full">
                      Featured Event
                    </span>
                  </div>
                )}
                <div className="absolute bottom-4 right-4">
                  <div className="flex items-center gap-1 text-white text-sm">
                    <Users size={16} />
                    <span>{event.attendees}+ attending</span>
                  </div>
                </div>
              </div>

              {/* Event Details */}
              <div className="space-y-4">
                <h3 className="text-xl font-serif font-bold text-charcoal-900 group-hover:text-gold-600 transition-colors duration-300">
                  {event.title}
                </h3>
                
                <p className="text-charcoal-600 text-sm leading-relaxed">
                  {event.description}
                </p>

                <div className="space-y-2">
                  <div className="flex items-center gap-3 text-charcoal-700">
                    <Calendar size={16} className="text-gold-500" />
                    <span className="text-sm font-medium">{event.date}</span>
                  </div>
                  <div className="flex items-center gap-3 text-charcoal-700">
                    <Clock size={16} className="text-gold-500" />
                    <span className="text-sm">{event.time}</span>
                  </div>
                  <div className="flex items-center gap-3 text-charcoal-700">
                    <MapPin size={16} className="text-gold-500" />
                    <div className="text-sm">
                      <div className="font-medium">{event.location}</div>
                      <div className="text-charcoal-500">{event.address}</div>
                    </div>
                  </div>
                </div>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full mt-6 px-6 py-3 bg-gradient-luxury from-gold-500 to-rose-500 text-white font-medium rounded-full 
                           transform transition-all duration-300 hover:shadow-luxury group"
                >
                  <span className="flex items-center justify-center gap-2">
                    Get Event Details
                    <ArrowRight size={16} className="group-hover:translate-x-1 transition-transform duration-300" />
                  </span>
                </motion.button>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="luxury-card max-w-3xl mx-auto shadow-depth-lg backdrop-blur-luxury bg-white/40 border border-white/30">
            <h3 className="text-3xl font-serif font-bold text-charcoal-900 mb-6">
              Never Miss an Event
            </h3>
            <p className="text-lg text-charcoal-700 mb-8 font-medium">
              Subscribe to our newsletter and be the first to know about <span className="text-gold-700 font-semibold">upcoming pop-up events</span>,
              exclusive previews, and <span className="text-rose-700 font-semibold">special discounts</span>.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-6 py-4 border-2 border-gold-200 rounded-full focus:outline-none focus:ring-2 focus:ring-gold-300 focus:border-gold-400 backdrop-blur-luxury bg-white/60 text-lg shadow-depth"
              />
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-10 py-4 bg-gradient-luxury from-gold-500 to-rose-500 text-white font-bold text-lg rounded-full
                         hover:shadow-luxury transition-all duration-300 shadow-depth-lg backdrop-blur-luxury border border-white/20"
              >
                Subscribe
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default PopUpEvents
